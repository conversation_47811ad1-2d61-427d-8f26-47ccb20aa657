<template>
  <a-modal
    title="正极材料核算 - 化学元素价格与正极材料价格"
    :visible="visible"
    :width="1200"
    :footer="null"
    @cancel="handleCancel"
    :destroyOnClose="true"
    :bodyStyle="{ padding: '12px 16px 16px 16px', height: '650px', overflow: 'hidden' }"
  >
    <div class="positive-electrode-accounting">
      <!-- 使用tableIndex组件结构 -->
      <tableIndex
        ref="pbiTableIndex"
        :pageLevel='2'
        :pageTitleShow="true"
        pageTitle="金属/盐价"
        :loading='loading'
        :paginationShow=false
        @tableFocus="tableFocus"
        @tableBlur="tableBlur"
      >
        <template #search>
          <pbiSearchContainer>
            <!-- 必填字段提示 -->
            <pbiSearchItem :span="24">
              <div style="background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 4px; padding: 6px 10px; margin-bottom: 12px; font-size: 12px; line-height: 1.4;">
                <span style="color: #52c41a; font-weight: 500;">📋 填写提示：</span>
                <span style="color: #389e0d;">
                  请确保所有化学元素的 <strong>单位</strong> 和 <strong>价格</strong>（人民币或美元至少填写一个）都已填写完整，才能进行正极材料价格计算。
                </span>
              </div>
            </pbiSearchItem>

            <!-- 美元汇率 -->
            <pbiSearchItem label="美元汇率(USD/CNY)" :span="6">
              <a-input-number
                v-model="usdExchangeRate"
                :precision="3"
                :min="0"
                :step="0.001"
                placeholder="请输入美元汇率"
                size="small"
                style="width: 100%"
                @change="onUsdExchangeRateChange"
                @blur="onUsdExchangeRateBlur"
              />
            </pbiSearchItem>

            <!-- 英镑汇率 -->
            <pbiSearchItem label="英镑汇率(GBP/CNY)" :span="6">
              <a-input-number
                v-model="gbpExchangeRate"
                :precision="3"
                :min="0"
                :step="0.001"
                placeholder="请输入英镑汇率"
                size="small"
                style="width: 100%"
                @change="onGbpExchangeRateChange"
                @blur="onGbpExchangeRateBlur"
              />
            </pbiSearchItem>

            <!-- 操作按钮 -->
            <pbiSearchItem type='btn' :span="12">
              <div class="main-btn">
                <a-button type="primary" @click="handleCalculate" size="small" :loading="calculating">计算正极材料价格</a-button>
              </div>
              <div class="main-btn">
                <a-button type="primary" @click="handleSaveResults" size="small" :loading="saving">保存计算结果</a-button>
              </div>
              <div class="main-btn">
                <a-button @click="handleCancel" size="small">关闭</a-button>
              </div>
            </pbiSearchItem>
          </pbiSearchContainer>
        </template>

        <template #table>
          <!-- 两个表格并排显示 -->
          <div style="display: flex; gap: 16px; margin-top: 8px; height: 100%;">
            <!-- 化学元素价格表格 -->
            <div style="flex: 1;">
              <div style="margin-bottom: 8px; font-weight: 500; color: #1890ff;">化学元素价格表</div>
              <ag-grid-vue
                :style="`height: ${tableHeight}px`"
                class="table ag-theme-balham"
                :tooltipShowDelay="0"
                :columnDefs="columnDefs"
                :rowData="rowData"
                :gridOptions="gridOptions"
                :defaultColDef="defaultColDef"
                :frameworkComponents="frameworkComponents"
                :suppressDragLeaveHidesColumns="true"
                :suppressMoveWhenColumnDragging="true"
                @grid-ready="onGridReady"
              />
            </div>

            <!-- 正极材料价格表格 -->
            <div style="flex: 1;">
              <div style="margin-bottom: 8px; font-weight: 500; color: #1890ff;">正极材料价格表</div>
              <ag-grid-vue
                :style="`height: ${tableHeight}px`"
                class="table ag-theme-balham"
                :tooltipShowDelay="0"
                :columnDefs="materialPriceColumnDefs"
                :rowData="materialPriceData"
                :defaultColDef="defaultColDef"
                :suppressDragLeaveHidesColumns="true"
                :suppressMoveWhenColumnDragging="true"
                :gridOptions="materialPriceGridOptions"
                @grid-ready="onMaterialPriceGridReady"
              />
            </div>
          </div>
        </template>
      </tableIndex>
    </div>
  </a-modal>
</template>

<script>
import { AgGridVue } from 'ag-grid-vue'
import { getChemicalElementList } from '@/api/modular/system/chemicalElementManage'
import { getChemicalElementPriceFromAccounting, chemicalElementPriceAdd, chemicalElementPriceUpdate } from '@/api/modular/system/chemicalElementPriceManage'
import { getBomAccountingDetailList } from '@/api/modular/system/bomAccountingDetailManage'
import { getPositiveMaterialAccountingList } from '@/api/modular/system/positiveMaterialAccountingManage'
import { positiveMaterialPriceBatchSave } from '@/api/modular/system/positiveMaterialPriceManage'
// import { scenarioEdit, getScenarioList } from '@/api/modular/system/scenarioManage'

export default {
  name: 'PositiveElectrodeAccountingModal',
  components: {
    AgGridVue
  },
  data() {
    return {
      visible: false,
      loading: false,
      usdExchangeRate: 7.2000, // 美元汇率
      gbpExchangeRate: 9.1000, // 英镑汇率
      rowData: [],
      gridApi: null,
      columnApi: null,
      bomCostOverviewId: null,
      tableHeight: 400, // 表格高度
      accountingType: 2,
      saveTimers: new Map(), // 防抖定时器
      calculating: false, // 计算状态
      saving: false, // 保存状态
      materialPriceData: [], // 正极材料价格数据
      materialPriceGridApi: null, // 正极材料价格表格API
      allAccountingData: [], // 所有正极材料核算数据，用于分析正极体系
      // ag-grid配置 - 参考BomAccountingDetailTable
      defaultColDef: {
        flex:1,
        filter: false,
        floatingFilter: false,
        editable: false,
        sortable: true,
        resizable: true
      },

      gridOptions: {
        onCellValueChanged: this.onCellValueChanged,
        stopEditingWhenCellsLoseFocus: true,
        suppressRowTransform: true
      },
      
      frameworkComponents: {},
      
      columnDefs: [
        {
          headerName: "化学元素",
          field: "elementName",
          width: 150,
          pinned: 'left',
          align: "center",
          cellStyle: { fontWeight: '500' }
        },
        {
          headerName: "场景",
          field: "scenarioName",
          width: 100,
          align: "center",
          valueFormatter: params => {
            if (params.data.scenarioId) {
              return `场景${params.data.scenarioId}`
            }
            return '' // 不显示通用，显示空字符串
          }
        },
        {
          headerName: "化学元素单位 *",
          field: "unit",
          width: 120,
          align: "center",
          editable: false, // 禁用AG Grid的编辑
          cellRenderer: params => {
            const value = params.value || ''
            const rowId = params.node.id
            return `<input type="text"
                           value="${value}"
                           data-row-id="${rowId}"
                           data-field="unit"
                           style="width: 100%; height: 90%; border: 1px solid #ddd; background: transparent;
                                  text-align: center; outline: none; padding: 0; box-sizing: border-box;"
                           onchange="window.handlePositiveCellChange && window.handlePositiveCellChange(this)"
                           onblur="window.handlePositiveCellBlur && window.handlePositiveCellBlur(this)" />`
          }
        },
        {
          headerName: "人民币价格(CNY) *",
          field: "cnyPrice",
          width: 160,
          align: "center",
          editable: false, // 禁用AG Grid的编辑
          cellRenderer: params => {
            const value = params.value !== null && params.value !== undefined && params.value !== 0 ? Number(params.value).toFixed(3) : ''
            const rowId = params.node.id
            return `<input type="number"
                           value="${value}"
                           data-row-id="${rowId}"
                           data-field="cnyPrice"
                           step="0.001"
                           style="width: 100%; height: 90%; border: 1px solid #ddd; background: transparent;
                                  text-align: center; outline: none; padding: 0; box-sizing: border-box;"
                           onchange="window.handlePositiveCellChange && window.handlePositiveCellChange(this)"
                           onblur="window.handlePositiveCellBlur && window.handlePositiveCellBlur(this)" />`
          }
        },
        {
          headerName: "外币价格 *",
          field: "foreignPrice",
          width: 160,
          align: "center",
          editable: false, // 禁用AG Grid的编辑
          cellRenderer: params => {
            const value = params.value !== null && params.value !== undefined && params.value !== 0 ? Number(params.value).toFixed(3) : ''
            const rowId = params.node.id
            return `<input type="number"
                           value="${value}"
                           data-row-id="${rowId}"
                           data-field="foreignPrice"
                           step="0.001"
                           style="width: 100%; height: 90%; border: 1px solid #ddd; background: transparent;
                                  text-align: center; outline: none; padding: 0; box-sizing: border-box;"
                           onchange="window.handlePositiveCellChange && window.handlePositiveCellChange(this)"
                           onblur="window.handlePositiveCellBlur && window.handlePositiveCellBlur(this)" />`
          }
        },
        {
          headerName: "币种",
          field: "currencyType",
          width: 100,
          align: "center",
          editable: false, // 禁用AG Grid的编辑
          cellRenderer: params => {
            const value = params.value || 'USD'
            const rowId = params.node.id
            const options = `
              <option value="USD" ${value === 'USD' ? 'selected' : ''}>USD</option>
              <option value="GBP" ${value === 'GBP' ? 'selected' : ''}>GBP</option>
            `
            return `<select data-row-id="${rowId}"
                            data-field="currencyType"
                            style="width: 100%; height: 90%; border: 1px solid #ddd; background: transparent;
                                   text-align: center; outline: none; padding: 0; box-sizing: border-box;"
                            onchange="window.handlePositiveCellChange && window.handlePositiveCellChange(this)"
                            onblur="window.handlePositiveCellBlur && window.handlePositiveCellBlur(this)">
                      ${options}
                    </select>`
          }
        }
      ],

      // 正极材料价格表格列定义
      materialPriceColumnDefs: [
        {
          headerName: "序号",
          width: 80,
          align: "center",
          cellRenderer: params => parseInt(params.node.id) + 1
        },
        {
          headerName: "场景",
          field: "scenarioName",
          width: 100,
          align: "center",
          sortable: true,
          comparator: (_valueA, _valueB, nodeA, nodeB) => {
            // 按场景ID排序，null值排在最后
            const scenarioIdA = nodeA.data.scenarioId
            const scenarioIdB = nodeB.data.scenarioId

            // null值排在最后
            if (scenarioIdA === null && scenarioIdB !== null) return 1
            if (scenarioIdA !== null && scenarioIdB === null) return -1
            if (scenarioIdA === null && scenarioIdB === null) return 0

            // 都不为null时，按场景ID升序排序
            return scenarioIdA - scenarioIdB
          }
        },
        {
          headerName: "正极体系编号",
          field: "chemicalSystemCode",
          width: 150,
          align: "center",
          editable: false, // 禁用AG Grid的编辑
          cellRenderer: params => {
            const isEditable = params.data.hasMultipleSystemCodes || false
            if (isEditable) {
              const value = params.value || ''
              const rowId = params.node.id
              const options = (params.data.availableSystemCodes || [params.data.chemicalSystemCode])
                .map(code => `<option value="${code}" ${code === value ? 'selected' : ''}>${code}</option>`)
                .join('')
              return `<select data-row-id="${rowId}"
                              data-field="chemicalSystemCode"
                              style="width: 100%; height: 90%; border: 1px solid #ddd; background: transparent;
                                     outline: none; padding: 0; box-sizing: border-box;"
                              onchange="window.handlePositiveCellChange && window.handlePositiveCellChange(this)">
                        ${options}
                      </select>`
            }
            return params.value || ''
          }
        },
        {
          headerName: "加工费(CNY)",
          field: "processingFee",
          width: 120,
          align: "center",
          editable: false, // 禁用AG Grid的编辑
          cellRenderer: params => {
            const value = params.value !== null && params.value !== undefined ? Number(params.value).toFixed(2) : ''
            const rowId = params.node.id
            return `<input type="number"
                           value="${value}"
                           data-row-id="${rowId}"
                           data-field="processingFee"
                           step="0.01"
                           style="width: 100%; height: 90%; border: 1px solid #ddd; background: transparent;
                                  text-align: center; outline: none; padding: 0; box-sizing: border-box;"
                           onchange="window.handlePositiveCellChange && window.handlePositiveCellChange(this)"
                           onblur="window.handlePositiveCellBlur && window.handlePositiveCellBlur(this)" />`
          }
        },
        {
          headerName: "材料价格(CNY)",
          field: "materialPrice",
          width: 130,
          align: "center",
          cellRenderer: params => {
            if (params.value !== null && params.value !== undefined) {
              return `¥${Number(params.value).toFixed(2)}`
            }
            return '-'
          },
          // 添加悬浮提示功能
          tooltipField: "materialPriceTooltip",
          tooltipComponentParams: {
            color: '#ffffcc',
            type: 'materialPriceTooltip'
          }
        },
        {
          headerName: "价格合计(CNY)",
          field: "totalPrice",
          width: 130,
          align: "center",
          cellRenderer: params => {
            if (params.value !== null && params.value !== undefined) {
              return `¥${Number(params.value).toFixed(2)}`
            }
            return '-'
          }
        }
      ],

      // 正极材料价格表格的gridOptions
      materialPriceGridOptions: {
        onCellValueChanged: this.onMaterialPriceCellValueChanged,
        stopEditingWhenCellsLoseFocus: true,
        suppressRowTransform: true
      }
  },

  computed: {
    // 选中行的总价格
    selectedRowsTotalPrice() {
      return this.selectedRows.reduce((sum, row) => sum + (row.totalPrice || 0), 0)
    },

    // 选中行涉及的场景文本
    selectedScenariosText() {
      const scenarios = [...new Set(this.selectedRows.map(row => row.scenarioName))]
      return scenarios.length > 3
        ? `${scenarios.slice(0, 3).join(', ')}等${scenarios.length}个场景`
        : scenarios.join(', ')
    }
  },

  methods: {
    // 显示弹窗
    show(bomCostOverviewId,accountingType) {
      this.visible = true
      this.bomCostOverviewId = bomCostOverviewId
      this.accountingType = accountingType
      this.loadData()
      this.loadMaterialPriceData()
    },
    
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        // 获取化学元素列表
        const elementsRes = await getChemicalElementList({
          accountingTypeIds: [this.accountingType] // 正极材料核算类型
        })
        
        if (elementsRes.success) {
          const elements = elementsRes.data
          
          // 获取化学元素价格数据（从正极材料核算表动态管理）
          const pricesRes = await getChemicalElementPriceFromAccounting(
            this.bomCostOverviewId,
            this.accountingType
          )
          
          const existingPrices = pricesRes.success ? pricesRes.data : []

          console.log('获取到的化学元素列表:', elements)
          console.log('获取到的价格数据:', existingPrices)

          // 创建化学元素ID到元素对象的映射，提高查找效率
          const elementMap = new Map()
          elements.forEach(element => {
            elementMap.set(element.id, element)
          })

          // 合并数据 - 显示所有化学元素的所有场景数据
          const resultData = []

          // 先处理已有的价格数据 - 每条价格数据都作为一行显示
          existingPrices.forEach(price => {
            const element = elementMap.get(price.chemicalElementId)
            // 优先使用后端返回的elementName，如果没有则从element对象获取
            const elementName = price.elementName || (element ? element.elementName : '')
            const unit = price.unit || (element ? element.unit : '')

            if (elementName) {
              resultData.push({
                id: price.id,
                chemicalElementId: price.chemicalElementId,
                elementName: elementName,
                scenarioId: price.scenarioId,
                scenarioName: price.scenarioId ? `场景${price.scenarioId}` : '',
                unit: unit,
                cnyPrice: price.cnyPrice || 0,
                foreignPrice: price.foreignPrice || 0,
                currencyType: price.currencyType || 'USD',
                bomCostOverviewId: this.bomCostOverviewId,
                positiveElectrodeAccountingType: this.accountingType
              })
            } else {
              console.warn('未找到化学元素名称，chemicalElementId:', price.chemicalElementId, 'price:', price)
            }
          })

          // 不再为没有价格数据的化学元素创建记录
          // 只显示已有价格数据的化学元素

          this.rowData = resultData

          console.log('最终显示的数据:', this.rowData)
          console.log('数据行数:', this.rowData.length)

          // 计算外币价格
          this.calculateAllForeignPrices()

          // 刷新表格样式以应用必填字段的视觉提示
          this.$nextTick(() => {
            this.refreshTableStyles()
          })
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        const errorMessage = error && error.message ? error.message : (error || '未知错误')
        this.$message.error('加载数据失败：' + errorMessage)
      } finally {
        this.loading = false
      }
    },

    // 加载正极材料价格数据（初始化时调用）
    async loadMaterialPriceData() {
      try {
        // 获取正极材料核算数据，用于分析正极体系
        const accountingRes = await getPositiveMaterialAccountingList({})
        if (accountingRes && accountingRes.success) {
          this.allAccountingData = accountingRes.data
          console.log('加载正极材料核算数据成功:', this.allAccountingData.length, '条')
        }
      } catch (error) {
        console.error('加载正极材料核算数据失败:', error)
      }
    },

    // 计算所有外币价格（初始化时调用）
    calculateAllForeignPrices() {
      this.rowData.forEach(row => {
        const currencyType = row.currencyType || 'USD'
        const exchangeRate = currencyType === 'USD' ? this.usdExchangeRate : this.gbpExchangeRate

        // 如果有人民币价格但没有外币价格，则计算外币价格
        if (row.cnyPrice && row.cnyPrice > 0 && exchangeRate > 0) {
          if (!row.foreignPrice || row.foreignPrice === 0) {
            row.foreignPrice = (row.cnyPrice / exchangeRate).toFixed(3)
          }
        }
        // 如果有外币价格但没有人民币价格，则计算人民币价格
        else if (row.foreignPrice && row.foreignPrice > 0 && exchangeRate > 0) {
          if (!row.cnyPrice || row.cnyPrice === 0) {
            row.cnyPrice = (row.foreignPrice * exchangeRate).toFixed(3)
          }
        }
      })

      // 刷新表格
      if (this.gridApi) {
        this.gridApi.setRowData(this.rowData)
        this.gridApi.refreshCells({ force: true })
      }
    },





    // 表格准备就绪
    onGridReady(params) {
      this.gridApi = params.api
      this.columnApi = params.columnApi
    },

    // 刷新表格样式
    refreshTableStyles() {
      if (this.gridApi) {
        // 刷新所有单元格以重新应用样式
        this.gridApi.refreshCells({
          force: true,
          suppressFlash: true
        })
      }
    },
    
    // 单元格值变更处理 - 实时保存（带防抖）
    onCellValueChanged(params) {
      const { data, colDef, newValue, oldValue, api, isBlur } = params

      // 如果值没有变化，不处理
      if (newValue === oldValue) {
        return
      }

      console.log(`字段 ${colDef.field} 从 ${oldValue} 变更为 ${newValue}${isBlur ? ' (失去焦点)' : ''}`)

      // 获取当前行的币种和对应汇率
      const currencyType = data.currencyType || 'USD'
      const exchangeRate = currencyType === 'USD' ? this.usdExchangeRate : this.gbpExchangeRate

      // 如果是人民币价格变更，自动计算外币价格
      if (colDef.field === 'cnyPrice' && exchangeRate > 0) {
        data.foreignPrice = (newValue / exchangeRate).toFixed(3)
        if (api && api.redrawRows) {
          api.redrawRows({ rowNodes: [params.node] })
        }
        console.log(`人民币价格变更，自动计算外币价格: ${newValue} / ${exchangeRate} = ${data.foreignPrice}`)
      }

      // 如果是外币价格变更，自动计算人民币价格
      if (colDef.field === 'foreignPrice' && exchangeRate > 0) {
        data.cnyPrice = (newValue * exchangeRate).toFixed(3)
        if (api && api.redrawRows) {
          api.redrawRows({ rowNodes: [params.node] })
        }
        console.log(`${currencyType}价格变更，自动计算人民币价格: ${newValue} * ${exchangeRate} = ${data.cnyPrice}`)
      }

      // 如果是币种变更，重新计算外币价格
      if (colDef.field === 'currencyType') {
        const newExchangeRate = newValue === 'USD' ? this.usdExchangeRate : this.gbpExchangeRate
        if (data.cnyPrice && data.cnyPrice > 0 && newExchangeRate > 0) {
          data.foreignPrice = (data.cnyPrice / newExchangeRate).toFixed(3)
          if (api && api.redrawRows) {
            api.redrawRows({ rowNodes: [params.node] })
          }
          console.log(`币种变更，重新计算${newValue}价格: ${data.cnyPrice} / ${newExchangeRate} = ${data.foreignPrice}`)
        }
      }

      // 刷新表格样式以更新必填字段的视觉提示
      this.refreshTableStyles()

      // 如果是失去焦点事件，立即保存；否则使用防抖
      if (isBlur) {
        this.saveSingleRecordImmediately(data, colDef.field)
      } else {
        this.debouncedSave(data, colDef.field)
      }
    },

    // 立即保存单条记录（失去焦点时调用）
    async saveSingleRecordImmediately(data, fieldName) {
      const saveKey = `${data.chemicalElementId}_${fieldName}`

      // 清除防抖定时器（如果存在）
      if (this.saveTimers.has(saveKey)) {
        clearTimeout(this.saveTimers.get(saveKey))
        this.saveTimers.delete(saveKey)
      }

      await this.saveSingleRecord(data, fieldName)
    },

    // 防抖保存
    debouncedSave(data, fieldName) {
      const saveKey = `${data.chemicalElementId}_${fieldName}`

      // 清除之前的定时器
      if (this.saveTimers.has(saveKey)) {
        clearTimeout(this.saveTimers.get(saveKey))
      }

      // 设置新的定时器，500ms后执行保存
      const timer = setTimeout(async () => {
        await this.saveSingleRecord(data, fieldName)
        this.saveTimers.delete(saveKey)
      }, 500)

      this.saveTimers.set(saveKey, timer)
    },

    // 保存单条记录
    async saveSingleRecord(data, fieldName) {
      try {
        const saveData = {
          id: data.id,
          bomCostOverviewId: data.bomCostOverviewId,
          scenarioId: data.scenarioId || null,
          chemicalElementId: data.chemicalElementId,
          positiveElectrodeAccountingType: data.positiveElectrodeAccountingType,
          unit: data.unit,
          cnyPrice: data.cnyPrice,
          foreignPrice: data.foreignPrice,
          currencyType: data.currencyType || 'USD'
        }

        console.log('保存单条记录:', saveData)

        let result
        if (data.id) {
          // 更新已有记录
          result = await chemicalElementPriceUpdate(saveData)
        } else {
          // 新增记录
          result = await chemicalElementPriceAdd(saveData)
          if (result.success && result.data) {
            // 更新行数据的ID
            data.id = result.data.id || result.data
          }
        }

        if (result.success) {
          const fieldNameMap = {
            unit: '单位',
            cnyPrice: '人民币价格',
            foreignPrice: '外币价格',
            currencyType: '币种'
          }
          this.$message.success(`${fieldNameMap[fieldName] || fieldName}更新成功`)
        } else {
          this.$message.error('保存失败：' + result.message)
        }

      } catch (error) {
        console.error('保存失败:', error)
        const errorMessage = error && error.message ? error.message : (error || '未知错误')
        this.$message.error('保存失败：' + errorMessage)
      }
    },
    
    // 取消/关闭
    handleCancel() {
      // 关闭弹窗
      this.visible = false
      this.rowData = []
      this.bomCostOverviewId = null
      this.materialPriceData = []
      this.allAccountingData = []
    },

    // 刷新数据（供外部调用）
    refreshData() {
      if (this.visible && this.bomCostOverviewId) {
        console.log('刷新正极核算数据')
        this.loadData()
        this.loadMaterialPriceData()
      }
    },

    // 验证必填字段
    validateRequiredFields() {
      if (!this.rowData || this.rowData.length === 0) {
        return {
          isValid: false,
          message: '没有化学元素价格数据，请先加载数据'
        }
      }

      // 检查汇率是否填写
      if (!this.exchangeRate || this.exchangeRate <= 0) {
        return {
          isValid: false,
          message: '请填写有效的汇率'
        }
      }

      // 检查每行数据的必填字段
      const missingFields = []
      const emptyRows = []

      for (let i = 0; i < this.rowData.length; i++) {
        const row = this.rowData[i]
        const rowNumber = i + 1
        const elementName = row.elementName || '未知元素'

        // 检查单位
        if (!row.unit || row.unit.trim() === '') {
          missingFields.push(`第${rowNumber}行 (${elementName}): 化学元素单位`)
        }

        // 检查价格（至少需要填写人民币价格或外币价格中的一个）
        const hasCnyPrice = row.cnyPrice !== null && row.cnyPrice !== undefined && row.cnyPrice > 0
        const hasForeignPrice = row.foreignPrice !== null && row.foreignPrice !== undefined && row.foreignPrice > 0

        if (!hasCnyPrice && !hasForeignPrice) {
          emptyRows.push(`第${rowNumber}行 (${elementName}): 人民币价格或外币价格`)
        }
      }

      // 如果有缺失字段，返回错误信息
      if (missingFields.length > 0 || emptyRows.length > 0) {
        let message = '请完善以下必填信息后再进行计算：\n'

        if (missingFields.length > 0) {
          message += '\n【缺少单位】\n' + missingFields.join('\n')
        }

        if (emptyRows.length > 0) {
          message += '\n\n【缺少价格】\n' + emptyRows.join('\n')
          message += '\n\n提示：每行至少需要填写人民币价格或外币价格中的一个'
        }

        return {
          isValid: false,
          message: message
        }
      }

      return {
        isValid: true,
        message: '验证通过'
      }
    },

    // 显示验证错误信息
    showValidationError(message) {
      try {
        console.log('显示验证错误信息:', message)

        // 使用简单的消息提示，避免复杂的模态框可能导致的问题
        this.$message.error({
          content: message,
          duration: 10, // 显示10秒
          style: {
            marginTop: '20vh',
          }
        })

        // 同时在控制台输出详细信息
        console.warn('数据验证失败:', message)
      } catch (error) {
        console.error('显示验证错误信息失败:', error)
        // 如果连消息提示都失败了，至少在控制台显示
        alert('数据验证失败: ' + message)
      }
    },

    // 计算正极材料价格
    async handleCalculate() {
      this.calculating = true

      try {
        console.log('开始计算正极材料价格')

        // 验证必填字段
        console.log('开始验证必填字段')
        let validationResult
        try {
          validationResult = this.validateRequiredFields()
          console.log('验证结果:', validationResult)
        } catch (validationError) {
          console.error('验证方法执行失败:', validationError)
          this.$message.error('数据验证过程中发生错误: ' + (validationError.message || validationError))
          return
        }

        if (!validationResult || !validationResult.isValid) {
          const errorMessage = validationResult && validationResult.message ? validationResult.message : '数据验证失败'
          console.log('验证失败，显示错误信息:', errorMessage)
          this.showValidationError(errorMessage)
          return
        }

        console.log('验证通过，继续执行计算')

        // 1. 获取BOM核算明细数据（过滤正极体系编号不为空的正极材料）
        const bomDetailRes = await getBomAccountingDetailList({
          bomCostOverviewId: this.bomCostOverviewId
        })

        if (!bomDetailRes || !bomDetailRes.success) {
          const errorMessage = bomDetailRes && bomDetailRes.message ? bomDetailRes.message : '获取BOM核算明细失败'
          this.$message.error('获取BOM核算明细失败: ' + errorMessage)
          return
        }

        // 1.1 获取场景数据，用于获取每个BOM明细对应的正极体系编号
        const scenarioApi = await import('@/api/modular/system/scenarioManage')
        const scenarioRes = await scenarioApi.getScenarioList({
          bomCostOverviewId: this.bomCostOverviewId
        })

        let scenarioMap = new Map()
        if (scenarioRes.success && scenarioRes.data) {
          // 建立bomAccountingDetailId到场景数据的映射
          scenarioRes.data.forEach(scenario => {
            if (scenario.bomAccountingDetailId) {
              if (!scenarioMap.has(scenario.bomAccountingDetailId)) {
                scenarioMap.set(scenario.bomAccountingDetailId, [])
              }
              scenarioMap.get(scenario.bomAccountingDetailId).push(scenario)
            }
          })
          console.log('场景数据映射:', scenarioMap)
        }

        // 过滤出有对应场景数据的正极材料
        const positiveMaterials = (bomDetailRes.data || []).filter(item => {
          if (!item) return false

          // 检查是否有对应的场景数据
          const scenarios = scenarioMap.get(item.id) || []
          if (scenarios.length > 0) {
            // 检查是否至少有一个场景包含正极材料核算ID
            const hasValidScenario = scenarios.some(s => s.positiveMaterialAccountingId)
            if (hasValidScenario) {
              // 保存所有场景数据供后续使用
              item.scenarioData = scenarios
              console.log(`BOM明细${item.id}有${scenarios.length}个场景数据`)
              return true
            } else {
              console.log(`BOM明细${item.id}的所有场景中都没有正极材料核算ID，跳过`)
              return false
            }
          } else {
            console.log(`BOM明细${item.id}没有对应的场景数据，跳过`)
            return false
          }
        })

        console.log('正极材料数据:', positiveMaterials)
        console.log('正极材料数量:', positiveMaterials.length)

        if (positiveMaterials.length === 0) {
          this.$message.warning('没有找到正极体系编号不为空的正极材料')
          console.log('所有BOM明细数据:', bomDetailRes.data)
          return
        }

        // 2. 获取正极材料核算数据
        const accountingRes = await getPositiveMaterialAccountingList({})

        if (!accountingRes || !accountingRes.success) {
          const errorMessage = accountingRes && accountingRes.message ? accountingRes.message : '获取正极材料核算数据失败'
          this.$message.error('获取正极材料核算数据失败: ' + errorMessage)
          return
        }

        console.log('正极材料核算数据:', accountingRes.data)

        // 保存所有正极材料核算数据，用于分析正极体系
        this.allAccountingData = accountingRes.data

        // 3. 计算每个正极材料的价格
        const materialPriceResults = []

        for (const material of positiveMaterials) {
          // 为每个场景计算价格（如果有多个场景的化学元素价格数据）
          const scenarios = this.getUniqueScenarios()

          if (scenarios.length === 0) {
            // 没有场景数据，跳过计算
            console.log('没有场景数据，跳过材料:', material.materialName)
          } else {
            // 为每个场景计算价格
            for (const scenario of scenarios) {
              const result = await this.calculateMaterialPrice(material, accountingRes.data, scenario)
              if (result) {
                materialPriceResults.push(result)
              }
            }
          }
        }

        // 按场景排序：按场景ID排序，null值排在最后
        materialPriceResults.sort((a, b) => {
          // null值排在最后
          if (a.scenarioId === null && b.scenarioId !== null) return 1
          if (a.scenarioId !== null && b.scenarioId === null) return -1
          if (a.scenarioId === null && b.scenarioId === null) return 0

          // 都不为null时，按场景ID升序排序
          return a.scenarioId - b.scenarioId
        })

        this.materialPriceData = materialPriceResults

        console.log('计算完成，正极材料价格数据（已按场景排序）:', this.materialPriceData)
        console.log('场景排序顺序:', this.materialPriceData.map(item => `${item.scenarioName}(ID:${item.scenarioId})`).join(' → '))
        this.$message.success(`计算完成，共计算 ${materialPriceResults.length} 个正极材料价格`)

      } catch (error) {
        console.error('计算失败:', error)
        const errorMessage = error && error.message ? error.message : (error || '未知错误')
        this.$message.error('计算失败: ' + errorMessage)
      } finally {
        this.calculating = false
      }
    },

    // 获取唯一的场景列表
    getUniqueScenarios() {
      const scenarios = new Set()
      this.rowData.forEach(row => {
        if (row.scenarioId) {
          scenarios.add(row.scenarioId)
        }
      })
      return Array.from(scenarios).map(id => ({
        scenarioId: id,
        scenarioName: `场景${id}`
      }))
    },



    // 计算单个材料的价格
    async calculateMaterialPrice(bomDetail, accountingData, scenario = null) {
      try {
        // 根据场景ID和BOM核算明细ID获取对应的正极材料核算ID
        let positiveMaterialAccountingId = bomDetail.positiveMaterialAccountingId // 默认值
        let chemicalSystemCode = bomDetail.chemicalSystemCode // 用于显示

        if (scenario && scenario.scenarioId && bomDetail.scenarioData) {
          // 从场景数据中找到对应场景ID的正极材料核算ID
          const targetScenario = bomDetail.scenarioData.find(s => s.scenarioId === scenario.scenarioId)
          if (targetScenario && targetScenario.positiveMaterialAccountingId) {
            positiveMaterialAccountingId = targetScenario.positiveMaterialAccountingId
            // 同时更新正极体系编号用于显示
            const accounting = accountingData.find(item => item.id === positiveMaterialAccountingId)
            if (accounting) {
              chemicalSystemCode = accounting.chemicalSystemCode
            }
            console.log(`场景${scenario.scenarioId}使用场景表中的正极材料核算ID: ${positiveMaterialAccountingId}, 正极体系编号: ${chemicalSystemCode}`)
          } else {
            console.log(`场景${scenario.scenarioId}在场景数据中未找到对应的正极材料核算ID，使用默认值: ${positiveMaterialAccountingId}`)
          }
        }

        console.log(`计算材料 ${bomDetail.materialName} 场景${scenario?.scenarioId || '默认'} 的价格:`)
        console.log('使用的正极材料核算ID:', positiveMaterialAccountingId)
        console.log('使用的正极体系编号:', chemicalSystemCode)
        console.log('BOM明细数据:', bomDetail)
        console.log('场景数据:', bomDetail.scenarioData)

        // 根据正极材料核算ID找到对应的核算数据
        const accounting = accountingData.find(item =>
          item.id === positiveMaterialAccountingId
        )

        if (!accounting) {
          console.warn(`未找到正极材料核算ID ${positiveMaterialAccountingId} 的核算数据`)
          return null
        }

        console.log('正极材料核算数据:', accounting)

        // 解析化学管理元素JSON
        // 注意：后端已经将JSON解析后的字段直接放在了JSONObject中
        let chemicalElements = {}

        if (accounting.chemicalManagementElementsJson) {
          try {
            chemicalElements = JSON.parse(accounting.chemicalManagementElementsJson)
          } catch (e) {
            console.error('解析化学管理元素JSON失败:', e)
            // 如果JSON解析失败，尝试直接从accounting对象中获取元素字段
            // 后端已经将JSON字段展开到了对象中
            for (const key in accounting) {
              if (key.includes('_') && key !== 'chemicalManagementElementsJson') {
                chemicalElements[key] = accounting[key]
              }
            }
          }
        } else {
          // 直接从accounting对象中获取元素字段
          for (const key in accounting) {
            if (key.includes('_') && key !== 'chemicalManagementElementsJson') {
              chemicalElements[key] = accounting[key]
            }
          }
        }

        console.log('化学元素使用量:', chemicalElements)

        // 计算材料价格：元素A使用量 * 元素A价格 + 元素B使用量 * 元素B价格 + ...
        let materialPrice = 0
        const priceDetails = []
        const tooltipDetails = [] // 用于生成悬浮提示

        for (const [key, usage] of Object.entries(chemicalElements)) {
          // key格式: "accountingType_elementName"
          const [accountingType, elementName] = key.split('_')

          if (accountingType == this.accountingType) {
            // 过滤掉使用量为null、undefined、空字符串或无效数值的数据
            if (usage === null || usage === undefined || usage === '' || isNaN(parseFloat(usage)) || parseFloat(usage) <= 0) {
              console.log(`跳过元素 ${elementName}，使用量无效: ${usage}`)
              continue
            }

            const usageValue = parseFloat(usage)
            console.log(`处理元素 ${elementName}，使用量: ${usageValue}`)

            // 从当前化学元素价格数据中找到对应元素的价格
            let elementPrice
            if (scenario) {
              // 如果指定了场景，查找该场景的价格
              elementPrice = (this.rowData || []).find(row =>
                row && row.elementName === elementName &&
                row.scenarioId === scenario.scenarioId &&
                row.cnyPrice > 0
              )
            } else {
              // 没有指定场景，跳过查找
              elementPrice = null
            }

            // 添加到悬浮提示详情（只添加有效的使用量）
            tooltipDetails.push(`${elementName}: ${usageValue}`)

            if (elementPrice && elementPrice.cnyPrice && parseFloat(elementPrice.cnyPrice) > 0) {
              const elementCost = usageValue * parseFloat(elementPrice.cnyPrice)

              // 检查计算结果是否有效
              if (!isNaN(elementCost) && isFinite(elementCost)) {
                materialPrice += elementCost

                priceDetails.push({
                  elementName: elementName,
                  usage: usageValue,
                  price: elementPrice.cnyPrice,
                  cost: elementCost
                })

                console.log(`${elementName}: ${usageValue} * ${elementPrice.cnyPrice} = ${elementCost}`)
              } else {
                console.warn(`元素 ${elementName} 计算结果无效: ${usageValue} * ${elementPrice.cnyPrice} = ${elementCost}`)
              }
            } else {
              console.warn(`未找到元素 ${elementName} 的有效价格数据，当前价格:`, elementPrice?.cnyPrice)
            }
          }
        }

        // 加工费从BomAccountingDetail获取
        // 注意：bomDetail是JSONObject，直接访问字段
        const processingFee = parseFloat(bomDetail.processingFee || 0)

        // 确保加工费和材料价格都是有效数值
        const validProcessingFee = isNaN(processingFee) || !isFinite(processingFee) ? 0 : processingFee
        let validMaterialPrice = isNaN(materialPrice) || !isFinite(materialPrice) ? 0 : materialPrice

        // 材料价格需要除以1000
        validMaterialPrice = validMaterialPrice / 1000

        // 价格合计 = 加工费 + 材料价格
        const totalPrice = validProcessingFee + validMaterialPrice

        console.log(`材料价格计算完成: 加工费=${validProcessingFee}, 材料价格=${validMaterialPrice} (已除以1000), 合计=${totalPrice}`)

        // 分析该正极体系是否有多个编号
        const chemicalSystemAnalysis = this.analyzeChemicalSystem(accounting.chemicalSystem)

        // 生成材料价格悬浮提示内容（只显示元素使用量）
        const materialPriceTooltip = tooltipDetails.length > 0
          ? tooltipDetails.join('\n')
          : '暂无元素使用量数据'

        return {
          bomAccountingDetailId: bomDetail.id,
          scenarioId: scenario ? scenario.scenarioId : null,
          scenarioName: scenario ? scenario.scenarioName : '',
          chemicalSystemCode: chemicalSystemCode, // 使用实际计算时的正极体系编号
          chemicalSystem: accounting.chemicalSystem, // 正极体系名称
          processingFee: validProcessingFee,
          materialPrice: validMaterialPrice,
          totalPrice: totalPrice,
          materialPriceTooltip: materialPriceTooltip, // 悬浮提示内容
          priceDetails: priceDetails, // 用于调试
          // 正极体系编号编辑相关
          hasMultipleSystemCodes: chemicalSystemAnalysis.hasMultiple,
          availableSystemCodes: chemicalSystemAnalysis.systemCodes
        }

      } catch (error) {
        console.error('计算材料价格失败:', error)
        return null
      }
    },

    // 分析正极体系是否有多个编号
    analyzeChemicalSystem(chemicalSystemName) {
      // 从所有正极材料核算数据中找到相同正极体系的所有编号
      const sameSystemItems = this.allAccountingData.filter(item =>
        item.chemicalSystem === chemicalSystemName
      )

      const systemCodes = [...new Set(sameSystemItems.map(item => item.chemicalSystemCode))]

      console.log(`正极体系 ${chemicalSystemName} 的编号分析:`, {
        总数量: sameSystemItems.length,
        唯一编号: systemCodes,
        是否有多个: systemCodes.length > 1
      })

      return {
        hasMultiple: systemCodes.length > 1,
        systemCodes: systemCodes
      }
    },

    // 正极材料价格表格准备就绪
    onMaterialPriceGridReady(params) {
      this.materialPriceGridApi = params.api
    },

    // 正极材料价格表格单元格值变更处理
    onMaterialPriceCellValueChanged(params) {
      const { data, colDef, newValue, oldValue, isBlur } = params

      // 如果值没有变化，不处理
      if (newValue === oldValue) {
        return
      }

      console.log(`正极材料价格字段 ${colDef.field} 从 ${oldValue} 变更为 ${newValue}${isBlur ? ' (失去焦点)' : ''}`)

      if (colDef.field === 'chemicalSystemCode') {
        // 正极体系编号变更，需要更新场景的化学编号并刷新化学元素价格表
        this.updateScenarioChemicalSystemCode(data, newValue)
      } else if (colDef.field === 'processingFee') {
        // 加工费变更，重新计算价格合计
        const validProcessingFee = parseFloat(newValue || 0)
        const validMaterialPrice = parseFloat(data.materialPrice || 0)
        data.totalPrice = validProcessingFee + validMaterialPrice

        // 刷新表格显示
        if (params.api) {
          if (params.api.redrawRows) {
            params.api.redrawRows({ rowNodes: [params.node] })
          } else {
            // 如果redrawRows不可用，使用refreshCells
            params.api.refreshCells({ rowNodes: [params.node] })
          }
        } else if (this.materialPriceGridApi) {
          // 如果params.api不可用，使用组件的materialPriceGridApi
          this.materialPriceGridApi.refreshCells({ rowNodes: [params.node] })
        }

        console.log(`加工费更新: 加工费=${validProcessingFee}, 材料价格=${validMaterialPrice}, 新的价格合计=${data.totalPrice}`)

        // 注意：加工费的保存逻辑在保存整个正极材料价格表时统一处理，这里不需要单独保存
      }
    },

    // 更新场景的正极体系编号并刷新化学元素价格表
    async updateScenarioChemicalSystemCode(data, newChemicalSystemCode) {
      try {
        // 根据新的正极体系编号找到对应的PositiveMaterialAccounting ID
        const newAccounting = this.allAccountingData.find(item =>
          item.chemicalSystemCode === newChemicalSystemCode
        )

        if (!newAccounting) {
          this.$message.error(`未找到正极体系编号 ${newChemicalSystemCode} 的核算数据`)
          return
        }

        console.log(`更新场景正极体系编号:`, {
          scenarioId: data.scenarioId,
          bomAccountingDetailId: data.bomAccountingDetailId,
          bomCostOverviewId: this.bomCostOverviewId,
          newChemicalSystemCode: newChemicalSystemCode,
          newPositiveMaterialAccountingId: newAccounting.id,
          originalData: data
        })

        // 1. 动态导入场景管理API
        const scenarioApi = await import('@/api/modular/system/scenarioManage')

        // 2. 先查询场景列表，找到对应的场景记录
        const queryParams = {
          bomCostOverviewId: this.bomCostOverviewId,
          scenarioId: data.scenarioId,
          bomAccountingDetailId: data.bomAccountingDetailId
        }
        console.log('查询场景参数:', queryParams)

        const scenarioListRes = await scenarioApi.getScenarioList(queryParams)
        console.log('场景查询结果:', scenarioListRes)

        if (!scenarioListRes.success || !scenarioListRes.data || scenarioListRes.data.length === 0) {
          console.error('未找到对应的场景记录，查询参数:', queryParams)
          this.$message.error('未找到对应的场景记录')
          return
        }

        // 如果有多条记录，找到匹配的那一条
        let scenario = null
        if (scenarioListRes.data.length === 1) {
          scenario = scenarioListRes.data[0]
        } else {
          // 多条记录时，精确匹配
          scenario = scenarioListRes.data.find(s =>
            s.scenarioId === data.scenarioId &&
            s.bomAccountingDetailId === data.bomAccountingDetailId
          )
          if (!scenario) {
            scenario = scenarioListRes.data[0] // 如果找不到精确匹配，使用第一条
          }
        }

        console.log('找到场景记录:', scenario)
        console.log('场景记录的主键ID:', scenario.id)

        // 3. 更新场景的正极材料核算ID
        const updateData = {
          id: scenario.id, // 必须包含主键ID
          positiveMaterialAccountingId: newAccounting.id
        }

        console.log('准备更新场景，更新数据:', updateData)
        console.log('原场景正极体系编号:', scenario.chemicalSystemCode, '-> 新编号:', newChemicalSystemCode)
        console.log('原场景正极材料核算ID:', scenario.positiveMaterialAccountingId, '-> 新ID:', newAccounting.id)

        const result = await scenarioApi.scenarioEdit(updateData)
        console.log('场景更新API返回结果:', result)

        if (!result.success) {
          console.error('更新场景正极体系编号失败:', result)
          this.$message.error('更新场景正极体系编号失败：' + result.message)
          return
        }

        console.log('场景正极体系编号更新成功，返回结果:', result)

        // 3. 等待一小段时间确保后端数据已更新
        await new Promise(resolve => setTimeout(resolve, 500))

        // 4. 刷新化学元素价格表数据
        console.log('开始刷新化学元素价格表数据...')
        await this.refreshChemicalElementPriceData()

        // 5. 重新计算该行的材料价格
        console.log('开始重新计算材料价格...')
        await this.recalculateMaterialPrice(data, newChemicalSystemCode)

        this.$message.success('正极体系编号更新成功，化学元素价格表已刷新')

      } catch (error) {
        console.error('更新场景正极体系编号失败:', error)
        const errorMessage = error && error.message ? error.message : (error || '未知错误')
        this.$message.error('更新场景正极体系编号失败: ' + errorMessage)
      }
    },

    // 刷新化学元素价格表数据
    async refreshChemicalElementPriceData() {
      try {
        console.log('刷新化学元素价格表数据，参数:', {
          bomCostOverviewId: this.bomCostOverviewId,
          accountingType: this.accountingType
        })

        // 重新调用getChemicalElementPriceFromAccounting接口
        const pricesRes = await getChemicalElementPriceFromAccounting(
          this.bomCostOverviewId,
          this.accountingType
        )

        console.log('getChemicalElementPriceFromAccounting 接口返回:', pricesRes)

        if (pricesRes.success) {
          const existingPrices = pricesRes.data
          console.log('刷新后的化学元素价格数据:', existingPrices)
          console.log('原有数据条数:', this.rowData.length, '新数据条数:', existingPrices.length)

          // 重新构建rowData
          const resultData = []
          existingPrices.forEach(price => {
            const elementName = price.elementName || ''
            const unit = price.unit || ''

            if (elementName) {
              resultData.push({
                id: price.id,
                chemicalElementId: price.chemicalElementId,
                elementName: elementName,
                scenarioId: price.scenarioId,
                scenarioName: price.scenarioId ? `场景${price.scenarioId}` : '',
                unit: unit,
                cnyPrice: price.cnyPrice || 0,
                foreignPrice: price.foreignPrice || 0,
                currencyType: price.currencyType || 'USD',
                bomCostOverviewId: this.bomCostOverviewId,
                positiveElectrodeAccountingType: this.accountingType
              })
            }
          })

          console.log('重新构建的数据:', resultData)
          this.rowData = resultData
          console.log('化学元素价格表数据已刷新，共', this.rowData.length, '条记录')

          // 刷新表格
          if (this.gridApi) {
            console.log('刷新ag-grid表格数据')
            this.gridApi.setRowData(this.rowData)
            // 强制刷新表格显示
            this.gridApi.refreshCells({ force: true })
          } else {
            console.warn('gridApi 不存在，无法刷新表格')
          }
        } else {
          console.error('getChemicalElementPriceFromAccounting 接口调用失败:', pricesRes.message)
          this.$message.error('刷新化学元素价格数据失败：' + pricesRes.message)
        }
      } catch (error) {
        console.error('刷新化学元素价格表数据失败:', error)
        this.$message.error('刷新化学元素价格数据失败：' + (error.message || error))
      }
    },

    // 重新计算材料价格（当正极体系编号变更时）
    async recalculateMaterialPrice(data, newChemicalSystemCode) {
      try {
        console.log(`重新计算材料价格，新的正极体系编号: ${newChemicalSystemCode}`)

        // 根据新的正极体系编号找到对应的正极材料核算数据
        const newAccounting = this.allAccountingData.find(item =>
          item.chemicalSystemCode === newChemicalSystemCode
        )

        if (!newAccounting) {
          this.$message.error(`未找到正极体系编号 ${newChemicalSystemCode} 的核算数据`)
          return
        }

        console.log(`找到对应的正极材料核算数据，ID: ${newAccounting.id}`)

        // 解析化学管理元素JSON
        let chemicalElements = {}

        if (newAccounting.chemicalManagementElementsJson) {
          try {
            chemicalElements = JSON.parse(newAccounting.chemicalManagementElementsJson)
          } catch (e) {
            // 从已展开的字段中获取
            for (const key in newAccounting) {
              if (key.includes('_') && key !== 'chemicalManagementElementsJson') {
                chemicalElements[key] = newAccounting[key]
              }
            }
          }
        } else {
          // 直接从accounting对象中获取元素字段
          for (const key in newAccounting) {
            if (key.includes('_') && key !== 'chemicalManagementElementsJson') {
              chemicalElements[key] = newAccounting[key]
            }
          }
        }

        // 重新计算材料价格
        let materialPrice = 0
        const tooltipDetails = [] // 用于生成悬浮提示

        for (const [key, usage] of Object.entries(chemicalElements)) {
          const [accountingType, elementName] = key.split('_')

          if (accountingType == this.accountingType) {
            // 过滤掉使用量为null、undefined、空字符串或无效数值的数据
            if (usage === null || usage === undefined || usage === '' || isNaN(parseFloat(usage)) || parseFloat(usage) <= 0) {
              console.log(`跳过元素 ${elementName}，使用量无效: ${usage}`)
              continue
            }

            const usageValue = parseFloat(usage)
            console.log(`处理元素 ${elementName}，使用量: ${usageValue}`)

            // 根据场景查找元素价格
            let elementPrice
            if (data.scenarioId) {
              elementPrice = this.rowData.find(row =>
                row.elementName === elementName &&
                row.scenarioId === data.scenarioId &&
                row.cnyPrice > 0
              )
            } else {
              elementPrice = this.rowData.find(row =>
                row.elementName === elementName && row.cnyPrice > 0
              )
            }

            // 添加到悬浮提示详情（只添加有效的使用量）
            tooltipDetails.push(`${elementName}: ${usageValue}`)

            if (elementPrice && elementPrice.cnyPrice && parseFloat(elementPrice.cnyPrice) > 0) {
              const elementCost = usageValue * parseFloat(elementPrice.cnyPrice)

              // 检查计算结果是否有效
              if (!isNaN(elementCost) && isFinite(elementCost)) {
                materialPrice += elementCost
                console.log(`${elementName}: ${usageValue} * ${elementPrice.cnyPrice} = ${elementCost}`)
              } else {
                console.warn(`元素 ${elementName} 计算结果无效: ${usageValue} * ${elementPrice.cnyPrice} = ${elementCost}`)
              }
            } else {
              console.warn(`未找到元素 ${elementName} 的有效价格数据，当前价格:`, elementPrice?.cnyPrice)
            }
          }
        }

        // 确保材料价格是有效数值
        let validMaterialPrice = isNaN(materialPrice) || !isFinite(materialPrice) ? 0 : materialPrice

        // 材料价格需要除以1000
        validMaterialPrice = validMaterialPrice / 1000

        const validProcessingFee = parseFloat(data.processingFee || 0)
        const validProcessingFeeValue = isNaN(validProcessingFee) || !isFinite(validProcessingFee) ? 0 : validProcessingFee

        // 更新数据
        data.chemicalSystemCode = newChemicalSystemCode
        data.materialPrice = validMaterialPrice
        data.totalPrice = validProcessingFeeValue + validMaterialPrice

        // 更新悬浮提示内容（只显示元素使用量）
        data.materialPriceTooltip = tooltipDetails.length > 0
          ? tooltipDetails.join('\n')
          : '暂无元素使用量数据'

        // 刷新表格行
        if (this.materialPriceGridApi) {
          this.materialPriceGridApi.redrawRows()
        }

        console.log(`材料价格重新计算完成: 材料价格=${validMaterialPrice} (已除以1000), 价格合计=${data.totalPrice}`)

      } catch (error) {
        console.error('重新计算材料价格失败:', error)
        const errorMessage = error && error.message ? error.message : (error || '未知错误')
        this.$message.error('重新计算材料价格失败: ' + errorMessage)
      }
    },

    // 保存计算结果到正极材料价格表
    async handleSaveResults() {
      this.saving = true

      try {
        console.log('开始保存正极材料价格计算结果')

        if (!this.materialPriceData || this.materialPriceData.length === 0) {
          this.$message.warning('没有计算结果可保存')
          return
        }

        // 准备保存数据，转换为后端需要的格式
        const saveData = this.materialPriceData.map(item => ({
          scenarioId: item.scenarioId,
          bomAccountingDetailId: item.bomAccountingDetailId,
          chemicalSystem: item.chemicalSystemCode, // 使用正极体系编号作为正极体系字段
          processingFee: item.processingFee,
          materialPrice: item.materialPrice,
          totalPrice: item.totalPrice
        }))

        console.log('保存数据详情:')
        saveData.forEach((item, index) => {
          console.log(`记录${index + 1}:`, {
            scenarioId: item.scenarioId,
            bomAccountingDetailId: item.bomAccountingDetailId,
            chemicalSystem: item.chemicalSystem,
            totalPrice: item.totalPrice
          })
        })

        console.log('准备保存的数据:', saveData)

        // 调用批量保存API
        const result = await positiveMaterialPriceBatchSave(saveData)

        if (result.success) {
          this.$message.success(`保存成功，共保存 ${saveData.length} 条正极材料价格记录，同时更新了对应场景的正极体系编号、未税单价、成本和占比`)
          console.log('保存成功:', result)

          // 保存成功后刷新主表数据并延迟关闭当前弹出层
          this.refreshMainTableData()

          // 延迟1.5秒关闭，让用户看到成功消息
          setTimeout(() => {
            this.closeModal()
          }, 1500)

        } else {
          this.$message.error('保存失败：' + result.message)
          console.error('保存失败:', result)
        }

      } catch (error) {
        console.error('保存失败:', error)
        const errorMessage = error && error.message ? error.message : (error || '未知错误')
        this.$message.error('保存失败：' + errorMessage)
      } finally {
        this.saving = false
      }
    },

    // 刷新主表数据
    refreshMainTableData() {
      try {
        console.log('正极材料价格保存成功，开始刷新主表数据')

        // 通过$emit向父组件发送刷新事件
        this.$emit('refresh-main-table')

        // 如果父组件有直接的刷新方法，也可以调用
        if (this.$parent && typeof this.$parent.refreshData === 'function') {
          this.$parent.refreshData()
          console.log('调用父组件refreshData方法')
        }

        // 如果有其他刷新方式，也可以在这里添加
        if (this.$parent && typeof this.$parent.loadData === 'function') {
          this.$parent.loadData()
          console.log('调用父组件loadData方法')
        }

      } catch (error) {
        console.error('刷新主表数据失败:', error)
      }
    },

    // 关闭弹出层
    closeModal() {
      console.log('关闭正极材料核算弹出层')

      // 重置所有状态
      this.visible = false
      this.materialPriceData = []
      this.rowData = []
      this.bomCostOverviewId = null
      this.allAccountingData = []
      this.calculating = false
      this.saving = false

      // 清理表格API引用
      this.gridApi = null
      this.materialPriceGridApi = null

      console.log('正极材料核算弹出层已关闭，状态已重置')
    },



    // tableIndex相关方法
    tableFocus() {
      // 表格获得焦点时的处理
    },

    tableBlur() {
      // 表格失去焦点时的处理
    }
  },

  mounted() {
    // 添加全局方法用于处理单元格变更
    window.handlePositiveCellChange = (inputElement, isBlur = false) => {
      const rowId = inputElement.getAttribute('data-row-id')
      const field = inputElement.getAttribute('data-field')
      const value = inputElement.value

      // 根据字段类型选择正确的gridApi
      let gridApi = null
      if (field === 'unit' || field === 'cnyPrice' || field === 'foreignPrice' || field === 'currencyType') {
        // 化学元素价格表的字段
        gridApi = this.gridApi
      } else if (field === 'processingFee' || field === 'chemicalSystemCode') {
        // 正极材料价格表的字段
        gridApi = this.materialPriceGridApi
      }

      if (gridApi && rowId && field) {
        // 找到对应的行数据
        const rowNode = gridApi.getRowNode(rowId)
        if (rowNode) {
          const oldValue = rowNode.data[field]
          let newValue = value

          // 根据字段类型转换值
          if (field === 'cnyPrice' || field === 'foreignPrice') {
            newValue = value === '' ? null : parseFloat(value)
          } else if (field === 'processingFee') {
            newValue = value === '' ? null : parseFloat(value)
          }

          // 更新数据
          rowNode.data[field] = newValue

          // 触发变更事件，传递是否为失去焦点事件
          if (field === 'unit' || field === 'cnyPrice' || field === 'foreignPrice' || field === 'currencyType') {
            // 化学元素价格表的字段
            this.onCellValueChanged({
              data: rowNode.data,
              oldValue: oldValue,
              newValue: newValue,
              colDef: { field: field },
              api: this.gridApi,
              node: rowNode,
              isBlur: isBlur
            })
          } else if (field === 'processingFee' || field === 'chemicalSystemCode') {
            // 正极材料价格表的字段
            this.onMaterialPriceCellValueChanged({
              data: rowNode.data,
              oldValue: oldValue,
              newValue: newValue,
              colDef: { field: field },
              api: this.materialPriceGridApi,
              node: rowNode,
              isBlur: isBlur
            })
          }
        }
      }
    }

    // 添加专门处理失去焦点的方法
    window.handlePositiveCellBlur = (inputElement) => {
      window.handlePositiveCellChange(inputElement, true)
    }
  },

  // 组件销毁时清理定时器
  beforeDestroy() {
    // 清理所有防抖定时器
    this.saveTimers.forEach(timer => {
      clearTimeout(timer)
    })
    this.saveTimers.clear()

    // 清理状态
    this.visible = false
    this.materialPriceData = []
    this.calculating = false
    this.saving = false
    this.materialPriceGridApi = null
    this.allAccountingData = []
    this.rowData = []
    this.bomCostOverviewId = null

    // 清理全局方法
    if (window.handlePositiveCellChange) {
      delete window.handlePositiveCellChange
    }
    if (window.handlePositiveCellBlur) {
      delete window.handlePositiveCellBlur
    }
  }
}
</script>

<style lang="less" scoped="">
@import '/src/components/pageTool/style/pbiSearchItem.less';
.positive-electrode-accounting {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* tableIndex组件在模态框中的样式调整 */
.positive-electrode-accounting /deep/ .pbi-table-index {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.positive-electrode-accounting /deep/ .pbi-table-content {
  flex: 1;
  overflow: hidden;
}



/* 表格样式 - 参考BomAccountingDetailTable */
@import '/src/components/pageTool/style/pbiSearchItem.less';

:root {
    --scroll-display: none;
    --scroll-border-bottom: none;
    --scroll-border-bottom-fixed: none;
}

/deep/.searchItem .label{
    width: initial;
}
.ant-breadcrumb a{
  color: rgba(0, 0, 0, 0.65);
}
.ant-breadcrumb {
	  font-size: 12px !important;
    margin: 12px;
}
/deep/.ag-body-horizontal-scroll{
    border-bottom: var(--scroll-border-bottom) !important;
}
/deep/.ag-body-horizontal-scroll-viewport {
    display: var(--scroll-display) !important;
    border-bottom: var(--scroll-border-bottom) !important;
}
/deep/.ag-horizontal-left-spacer,
/deep/.ag-horizontal-right-spacer{
    border-bottom: var(--scroll-border-bottom-fixed) !important;
}
/deep/.search-container .vue-treeselect__multi-value-label{
    white-space: nowrap;
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
}
/deep/.search-container .vue-treeselect__limit-tip-text{
    font-weight: initial;
    text-indent: -32px;
    overflow: hidden;
    margin: 0;
}

/* 按钮样式 - 参考BomAccountingDetailTable */
.main-btn {
  display: inline-block;
  margin-right: 8px;
}

.main-btn .ant-btn {
  margin-right: 8px;
}

.mr10 {
  margin-right: 10px !important;
}

/* 复选框列样式 */
/deep/ .ag-selection-checkbox {
  margin: 0 auto;
}

/deep/ .ag-header-select-all {
  margin: 0 auto;
}

/* 悬浮提示样式 */
/deep/ .ag-tooltip {
  background-color: #ffffcc !important;
  border: 1px solid #ccc !important;
  border-radius: 4px !important;
  padding: 8px !important;
  font-size: 12px !important;
  line-height: 1.4 !important;
  white-space: pre-line !important;
  max-width: 300px !important;
  word-wrap: break-word !important;
}
</style>
