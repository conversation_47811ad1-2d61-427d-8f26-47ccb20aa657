/**
 * BOM核算明细管理
 *
 * <AUTHOR>
 * @date 2025年7月1日
 */
import { axios } from '@/utils/request'

/**
 * BOM核算明细分页列表
 *
 * <AUTHOR>
 */
export function getBomAccountingDetailPage(parameter) {
  return axios({
    url: '/bomAccountingDetail/pageList',
    method: 'post',
    data: parameter
  })
}

/**
 * BOM核算明细列表
 *
 * <AUTHOR>
 */
export function getBomAccountingDetailList(parameter) {
  return axios({
    url: '/bomAccountingDetail/list',
    method: 'post',
    data: parameter
  })
}

/**
 * 新增BOM核算明细
 *
 * <AUTHOR>
 */
export function bomAccountingDetailAdd(parameter) {
  return axios({
    url: '/bomAccountingDetail/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑BOM核算明细
 *
 * <AUTHOR>
 */
export function bomAccountingDetailEdit(parameter) {
  return axios({
    url: '/bomAccountingDetail/update',
    method: 'post',
    data: parameter
  })
}


export function bomAccountingDetailUpdateBaseUse(parameter) {
  return axios({
    url: '/bomAccountingDetail/updateBaseUse',
    method: 'post',
    data: parameter
  })
}
/**
 * 删除BOM核算明细
 *
 * <AUTHOR>
 */
export function bomAccountingDetailDelete(parameter) {
  return axios({
    url: '/bomAccountingDetail/delete',
    method: 'post',
    data: parameter
  })
}

/**
 * 获取BOM核算明细详情
 *
 * <AUTHOR>
 */
export function bomAccountingDetailGet(parameter) {
  return axios({
    url: '/bomAccountingDetail/get',
    method: 'post',
    data: parameter
  })
}

/**
 * 导出BOM核算明细Excel
 *
 * <AUTHOR>
 */
export function bomAccountingDetailExport(parameter) {
  return axios.post('/bomAccountingDetail/export', {...parameter}, {responseType: 'blob'})
}

/**
 * 获取核算结果
 *
 * <AUTHOR>
 */
export function getBomAccountingResult(parameter) {
  return axios({
    url: '/bomAccountingDetail/getAccountingResult',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除BOM核算明细
 *
 * <AUTHOR>
 */
export function bomAccountingDetailDelete(parameter) {
  return axios({
    url: '/bomAccountingDetail/delete',
    method: 'post',
    data: parameter
  })
}

/**
 * 下载BOM文件模板
 *
 * <AUTHOR>
 */
export function downloadBomTemplate(parameter) {
  return axios.post('/bomAccountingDetail/downloadTemplate', {...parameter}, {responseType: 'blob'})
}
