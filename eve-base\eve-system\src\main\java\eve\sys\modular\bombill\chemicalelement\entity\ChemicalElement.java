package eve.sys.modular.bombill.chemicalelement.entity;

import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import eve.core.pojo.base.entity.BaseEntity;
import lombok.*;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("CHEMICAL_ELEMENT")
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ChemicalElement extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /** 元素名称 */
    private String elementName;

    /** 核算类型 */
    private String accountingType;

    /* 单位 */
    private String unit;

    /** 金属前提展示 0-否 1-是 */
    private Integer metalPremiseDisplay;

    private Integer sort;

    @TableField(exist = false)
    private List<String> accountingTypeIds;
}