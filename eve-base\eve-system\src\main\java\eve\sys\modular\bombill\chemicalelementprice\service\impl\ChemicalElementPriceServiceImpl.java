package eve.sys.modular.bombill.chemicalelementprice.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.core.exception.ServiceException;
import eve.core.pojo.page.PageResult;
import eve.sys.modular.bombill.chemicalelement.entity.ChemicalElement;
import eve.sys.modular.bombill.chemicalelement.service.IChemicalElementService;
import eve.sys.modular.bombill.chemicalelementprice.entity.ChemicalElementPrice;
import eve.sys.modular.bombill.chemicalelementprice.mapper.ChemicalElementPriceMapper;
import eve.sys.modular.bombill.chemicalelementprice.service.IChemicalElementPriceService;

import eve.sys.modular.bombill.positivematerialaccounting.entity.PositiveMaterialAccounting;
import eve.sys.modular.bombill.positivematerialaccounting.service.IPositiveMaterialAccountingService;
import eve.sys.modular.bombill.scenario.entity.Scenario;
import eve.sys.modular.bombill.scenario.service.IScenarioService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import com.alibaba.fastjson.JSONObject;

import javax.annotation.Resource;

/**
 * 化学元素价格Service实现类
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@Service
public class ChemicalElementPriceServiceImpl extends ServiceImpl<ChemicalElementPriceMapper, ChemicalElementPrice> implements IChemicalElementPriceService {

    @Override
    public PageResult<ChemicalElementPrice> pageList(ChemicalElementPrice param) {
        LambdaQueryWrapper<ChemicalElementPrice> queryWrapper = new LambdaQueryWrapper<>();
        
        Page<ChemicalElementPrice> page = new Page<>(param.getPageNo(), param.getPageSize());
        Page<ChemicalElementPrice> pageResult = this.page(page, queryWrapper);
        
        return new PageResult<>(pageResult);
    }

    @Override
    public List<ChemicalElementPrice> list(ChemicalElementPrice param) {

        LambdaQueryWrapper<ChemicalElementPrice> queryWrapper = new LambdaQueryWrapper<>();

        if (ObjectUtil.isNotNull(param.getBomCostOverviewId())) {
            queryWrapper.eq(ChemicalElementPrice::getBomCostOverviewId, param.getBomCostOverviewId());
        }

        if(ObjectUtil.isNotNull(param.getPositiveElectrodeAccountingType())){
            queryWrapper.eq(ChemicalElementPrice::getPositiveElectrodeAccountingType, param.getPositiveElectrodeAccountingType());
        }

        // 按创建时间倒序排列
        queryWrapper.orderByAsc(ChemicalElementPrice::getScenarioId);

        List<ChemicalElementPrice> priceList = this.list(queryWrapper);

        // 关联查询化学元素名称
        if (!priceList.isEmpty()) {
            enrichWithElementNames(priceList);
        }

        return priceList;
    }

    @Override
    public Boolean add(ChemicalElementPrice param) {
        return this.save(param);
    }

    @Override
    public Boolean delete(ChemicalElementPrice param) {
        /* if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        return this.removeById(param.getId()); */
        LambdaQueryWrapper<ChemicalElementPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChemicalElementPrice::getBomCostOverviewId, param.getBomCostOverviewId());
        queryWrapper.eq(ChemicalElementPrice::getScenarioId, param.getScenarioId());
        return this.remove(queryWrapper);
    }

    @Override
    public Boolean update(ChemicalElementPrice param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        ChemicalElementPrice updateEntity = ChemicalElementPrice.builder()
            .id(param.getId())
            .scenarioId(param.getScenarioId())
            .bomCostOverviewId(param.getBomCostOverviewId())
            .chemicalElementId(param.getChemicalElementId())
            .positiveElectrodeAccountingType(param.getPositiveElectrodeAccountingType())
            .cnyPrice(param.getCnyPrice())
            .foreignPrice(param.getForeignPrice())
            .currencyType(param.getCurrencyType())
            .unit(param.getUnit())
            .build();
        return this.updateById(updateEntity);
    }

    @Override
    public Boolean batchSave(List<ChemicalElementPrice> params) {
        if (ObjectUtil.isEmpty(params) || params.isEmpty()) {
            throw new ServiceException(400, "保存数据不能为空");
        }

        log.info("开始批量保存化学元素价格，共{}条数据", params.size());

        List<ChemicalElementPrice> insertList = new ArrayList<>();
        List<ChemicalElementPrice> updateList = new ArrayList<>();

        // 分离新增和更新的数据
        for (ChemicalElementPrice param : params) {
            if (ObjectUtil.isEmpty(param.getId())) {
                // 新增数据
                insertList.add(param);
            } else {
                // 更新数据
                updateList.add(param);
            }
        }

        log.info("新增数据{}条，更新数据{}条", insertList.size(), updateList.size());

        boolean result = true;

        // 批量新增
        if (!insertList.isEmpty()) {
            result = this.saveBatch(insertList);
            log.info("批量新增结果: {}", result);
        }

        // 批量更新
        if (!updateList.isEmpty() && result) {
            result = this.updateBatchById(updateList);
            log.info("批量更新结果: {}", result);
        }

        log.info("批量保存完成，最终结果: {}", result);
        return result;
    }

    @Override
    public ChemicalElementPrice get(ChemicalElementPrice param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        return this.getById(param.getId());
    }



    @Resource
    private IChemicalElementService chemicalElementService;

    @Resource
    private IPositiveMaterialAccountingService positiveMaterialAccountingService;

    @Resource
    private IScenarioService scenarioService;

    @Override
    public void insertBatch(Scenario scenario) {
        
        List<ChemicalElement> chemicalElements = chemicalElementService.list(ChemicalElement.builder().build());
        List<ChemicalElementPrice> chemicalElementPrices = new ArrayList<>();

        for (ChemicalElement chemicalElement : chemicalElements) {
            ChemicalElementPrice price = new ChemicalElementPrice();
            price.setBomCostOverviewId(scenario.getBomCostOverviewId());
            price.setScenarioId(scenario.getScenarioId());
            price.setChemicalElementId(chemicalElement.getId());
            price.setPositiveElectrodeAccountingType(Integer.valueOf(chemicalElement.getAccountingType()));
            price.setUnit(chemicalElement.getUnit());
            price.setCnyPrice(BigDecimal.ZERO);
            price.setForeignPrice(BigDecimal.ZERO);
            price.setCurrencyType("USD");
            chemicalElementPrices.add(price);
        }
        this.saveBatch(chemicalElementPrices);
    }

    @Override
    public void copyFromPreviousScenario(Long fromScenarioId, Scenario toScenario) {
        // 查询源场景的化学元素价格数据
        LambdaQueryWrapper<ChemicalElementPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChemicalElementPrice::getBomCostOverviewId, toScenario.getBomCostOverviewId());
        queryWrapper.eq(ChemicalElementPrice::getScenarioId, fromScenarioId);

        List<ChemicalElementPrice> sourcePrices = this.list(queryWrapper);

        if (sourcePrices.isEmpty()) {
            log.info("源场景{}没有化学元素价格数据，跳过复制", fromScenarioId);
            return;
        }

        // 复制价格数据到新场景
        List<ChemicalElementPrice> newPrices = new ArrayList<>();
        for (ChemicalElementPrice sourcePrice : sourcePrices) {
            ChemicalElementPrice newPrice = new ChemicalElementPrice();
            newPrice.setBomCostOverviewId(toScenario.getBomCostOverviewId());
            newPrice.setScenarioId(toScenario.getScenarioId());
            newPrice.setChemicalElementId(sourcePrice.getChemicalElementId());
            newPrice.setPositiveElectrodeAccountingType(sourcePrice.getPositiveElectrodeAccountingType());
            newPrice.setCnyPrice(sourcePrice.getCnyPrice());
            newPrice.setForeignPrice(sourcePrice.getForeignPrice());
            newPrice.setCurrencyType(sourcePrice.getCurrencyType());
            newPrice.setUnit(sourcePrice.getUnit());

            newPrices.add(newPrice);
        }

        // 批量保存新场景的价格数据
        this.saveBatch(newPrices);
        log.info("成功复制{}条化学元素价格数据从场景{}到场景{}", newPrices.size(), fromScenarioId, toScenario.getScenarioId());
    }

    /**
     * 为化学元素价格列表填充元素名称
     *
     * @param priceList 化学元素价格列表
     */
    private void enrichWithElementNames(List<ChemicalElementPrice> priceList) {
        // 获取所有化学元素ID
        List<Long> elementIds = priceList.stream()
                .map(ChemicalElementPrice::getChemicalElementId)
                .distinct()
                .collect(Collectors.toList());

        if (elementIds.isEmpty()) {
            return;
        }

        // 批量查询化学元素信息
        List<ChemicalElement> elements = chemicalElementService.listByIds(elementIds);
        Map<Long, String> elementNameMap = elements.stream()
                .collect(Collectors.toMap(ChemicalElement::getId, ChemicalElement::getElementName));

        // 填充元素名称
        priceList.forEach(price -> {
            String elementName = elementNameMap.get(price.getChemicalElementId());
            price.setElementName(elementName);
        });
    }

    @Override
    public List<ChemicalElementPrice> getChemicalElementPriceFromAccounting(Long bomCostOverviewId, Integer positiveElectrodeAccountingType) {
        log.info("根据正极材料核算表数据获取化学元素价格数据，bomCostOverviewId: {}, positiveElectrodeAccountingType: {}",
                bomCostOverviewId, positiveElectrodeAccountingType);

        try {
            // 1. 获取现有的化学元素价格数据
            List<ChemicalElementPrice> existingPrices = getExistingChemicalElementPrices(bomCostOverviewId, positiveElectrodeAccountingType);
            log.info("步骤1：获取到现有化学元素价格数据 {} 条", existingPrices.size());

            // 2. 从场景数据获取相关的正极材料核算ID
            Set<Long> positiveMaterialAccountingIds = getPositiveMaterialAccountingIdsByBomCostOverviewId(bomCostOverviewId);
            log.info("步骤2：获取到正极材料核算ID {}", positiveMaterialAccountingIds);

            if (positiveMaterialAccountingIds.isEmpty()) {
                log.info("未找到正极材料核算ID，直接返回现有数据");
                fillElementNames(existingPrices);
                return existingPrices;
            }

            // 3. 根据正极材料核算ID获取正极材料核算表数据
            List<PositiveMaterialAccounting> accountingList = positiveMaterialAccountingService.list(
                Wrappers.<PositiveMaterialAccounting>lambdaQuery()
                    .in(PositiveMaterialAccounting::getId, positiveMaterialAccountingIds)
            );
            log.info("步骤3：获取到正极材料核算数据 {} 条", accountingList != null ? accountingList.size() : 0);

            if (accountingList == null || accountingList.isEmpty()) {
                log.info("未找到正极材料核算数据，直接返回现有数据");
                fillElementNames(existingPrices);
                return existingPrices;
            }

            // 4. 从正极材料数据过滤掉未填写使用量的元素，获取有使用量的化学元素数据
            Set<ChemicalElementUsage> requiredElements = extractRequiredElements(accountingList, positiveElectrodeAccountingType, bomCostOverviewId);
            log.info("步骤4：提取到有使用量的化学元素 {} 个", requiredElements.size());

            // 5. 根据现有数据和需要的元素，进行批量删除和新增操作
            performBatchOperations(existingPrices, requiredElements, bomCostOverviewId, positiveElectrodeAccountingType);
            log.info("步骤5：完成批量删除和新增操作");

            // 6. 查询并返回最新的化学元素价格数据
            List<ChemicalElementPrice> result = getExistingChemicalElementPrices(bomCostOverviewId, positiveElectrodeAccountingType);
            fillElementNames(result);

            // 按场景ID升序，化学元素名称降序排序
            result.sort(Comparator
                    .comparing(ChemicalElementPrice::getScenarioId)
                    .thenComparing(ChemicalElementPrice::getElementName));

            log.info("返回排序后的化学元素价格数据: {} 条", result.size());
            log.info("返回最终结果 {} 条记录", result.size());

            return result;

        } catch (Exception e) {
            log.error("获取化学元素价格数据失败", e);
            throw new ServiceException(500, "获取化学元素价格数据失败: " + e.getMessage());
        }
    }



    /**
     * 解析使用量
     */
    private BigDecimal parseUsage(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }

        try {
            if (value instanceof Number) {
                return new BigDecimal(value.toString());
            } else if (value instanceof String) {
                String strValue = ((String) value).trim();
                if (strValue.isEmpty()) {
                    return BigDecimal.ZERO;
                }
                return new BigDecimal(strValue);
            }
        } catch (NumberFormatException e) {
            log.warn("解析使用量失败: {}", value, e);
        }

        return BigDecimal.ZERO;
    }



    /**
     * 通过bomCostOverviewId获取相关的正极材料核算ID
     */
    private Set<Long> getPositiveMaterialAccountingIdsByBomCostOverviewId(Long bomCostOverviewId) {
        // 查询所有场景，然后在代码中过滤
        LambdaQueryWrapper<Scenario> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Scenario::getBomCostOverviewId, bomCostOverviewId);
        queryWrapper.select(Scenario::getPositiveMaterialAccountingId);

        List<Scenario> allScenariosForFiltering = scenarioService.list(queryWrapper);

        // 检查查询结果是否为空
        if (allScenariosForFiltering == null || allScenariosForFiltering.isEmpty()) {
            log.warn("未找到bomCostOverviewId={}对应的场景数据", bomCostOverviewId);
            return new HashSet<>();
        }

        // 在代码中过滤有正极材料核算ID的场景
        Set<Long> positiveMaterialAccountingIds = allScenariosForFiltering.stream()
                .filter(Objects::nonNull)
                .map(Scenario::getPositiveMaterialAccountingId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        log.info("bomCostOverviewId={}找到{}个有效的正极材料核算ID", bomCostOverviewId, positiveMaterialAccountingIds.size());
        return positiveMaterialAccountingIds;
    }

    /**
     * 为化学元素价格数据填充元素名称
     */
    private void fillElementNames(List<ChemicalElementPrice> priceList) {
        if (priceList == null || priceList.isEmpty()) {
            return;
        }

        // 获取所有化学元素ID
        Set<Long> elementIds = priceList.stream()
                .map(ChemicalElementPrice::getChemicalElementId)
                .filter(id -> id != null)
                .collect(Collectors.toSet());

        if (elementIds.isEmpty()) {
            return;
        }

        // 批量查询化学元素信息
        LambdaQueryWrapper<ChemicalElement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ChemicalElement::getId, elementIds);
        List<ChemicalElement> elements = chemicalElementService.list(queryWrapper);

        // 创建ID到元素名称的映射
        Map<Long, String> elementNameMap = elements.stream()
                .collect(Collectors.toMap(ChemicalElement::getId, ChemicalElement::getElementName));

        // 填充元素名称
        priceList.forEach(price -> {
            String elementName = elementNameMap.get(price.getChemicalElementId());
            price.setElementName(elementName);
        });


    }

    /**
     * 获取现有的化学元素价格数据，按场景ID升序
     */
    private List<ChemicalElementPrice> getExistingChemicalElementPrices(Long bomCostOverviewId, Integer positiveElectrodeAccountingType) {
        LambdaQueryWrapper<ChemicalElementPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChemicalElementPrice::getBomCostOverviewId, bomCostOverviewId);
        queryWrapper.eq(ChemicalElementPrice::getPositiveElectrodeAccountingType, positiveElectrodeAccountingType);
        //queryWrapper.orderByAsc(ChemicalElementPrice::getScenarioId);
        return this.list(queryWrapper);
    }



    /**
     * 通过bomCostOverviewId获取场景ID对应化学体系编码的映射（一个场景ID可能对应多个化学体系编码）
     */
    private Map<Long, List<String>> getScenarioChemicalSystemCodeMap(Long bomCostOverviewId) {
        LambdaQueryWrapper<Scenario> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Scenario::getScenarioId, Scenario::getPositiveMaterialAccountingId);
        queryWrapper.eq(Scenario::getBomCostOverviewId, bomCostOverviewId);
        queryWrapper.isNotNull(Scenario::getPositiveMaterialAccountingId);
        queryWrapper.orderByAsc(Scenario::getScenarioId);

        List<Scenario> scenarios = scenarioService.list(queryWrapper);

        // 检查查询结果是否为空
        if (scenarios == null || scenarios.isEmpty()) {
            log.warn("未找到bomCostOverviewId={}对应的场景数据（带正极材料核算ID）", bomCostOverviewId);
            return new HashMap<>();
        }

        // 收集所有的positiveMaterialAccountingId
        Set<Long> accountingIds = scenarios.stream()
            .map(Scenario::getPositiveMaterialAccountingId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        if (accountingIds.isEmpty()) {
            log.warn("bomCostOverviewId={}的场景数据中未找到有效的正极材料核算ID", bomCostOverviewId);
            return new HashMap<>();
        }

        // 批量查询PositiveMaterialAccounting信息
        List<PositiveMaterialAccounting> accountingList = positiveMaterialAccountingService.list(
            Wrappers.<PositiveMaterialAccounting>lambdaQuery()
                .in(PositiveMaterialAccounting::getId, accountingIds)
                .select(PositiveMaterialAccounting::getId, PositiveMaterialAccounting::getChemicalSystemCode)
        );

        // 检查查询结果是否为空
        if (accountingList == null || accountingList.isEmpty()) {
            log.warn("未找到正极材料核算数据，accountingIds: {}", accountingIds);
            return new HashMap<>();
        }

        // 创建ID到化学体系编号的映射，处理可能的null值
        Map<Long, String> accountingMap = accountingList.stream()
            .filter(accounting -> accounting.getId() != null && accounting.getChemicalSystemCode() != null)
            .collect(Collectors.toMap(
                PositiveMaterialAccounting::getId,
                PositiveMaterialAccounting::getChemicalSystemCode,
                (existing, replacement) -> existing // 处理重复key的情况
            ));

        // 为场景填充化学体系编号并分组
        return scenarios.stream()
                .filter(scenario -> scenario.getPositiveMaterialAccountingId() != null)
                .peek(scenario -> {
                    String chemicalSystemCode = accountingMap.get(scenario.getPositiveMaterialAccountingId());
                    scenario.setChemicalSystemCode(chemicalSystemCode);
                })
                .filter(scenario -> scenario.getChemicalSystemCode() != null && !scenario.getChemicalSystemCode().trim().isEmpty())
                .collect(Collectors.groupingBy(
                    Scenario::getScenarioId,
                    Collectors.mapping(
                        Scenario::getChemicalSystemCode,
                        Collectors.toList()
                    )
                ));
    }

    /**
     * 从正极材料数据中提取有使用量的化学元素
     */
    private Set<ChemicalElementUsage> extractRequiredElements(List<PositiveMaterialAccounting> accountingList,
                                                            Integer positiveElectrodeAccountingType,
                                                            Long bomCostOverviewId) {
        Set<ChemicalElementUsage> requiredElements = new HashSet<>();

        // 获取所有化学元素
        ChemicalElement elementQuery = new ChemicalElement();
        elementQuery.setAccountingTypeIds(Arrays.asList(String.valueOf(positiveElectrodeAccountingType)) );
        List<ChemicalElement> allElements = chemicalElementService.list(elementQuery);
        Map<String, ChemicalElement> elementNameMap = allElements.stream()
                .collect(Collectors.toMap(e->  e.getAccountingType() + "_" + e.getElementName() , element -> element));

        // 获取场景ID对应化学体系编码的映射（一个场景ID可能对应多个化学体系编码）
        Map<Long, List<String>> scenarioChemicalSystemCodeMap = getScenarioChemicalSystemCodeMap(bomCostOverviewId);
        log.info("获取到的场景ID对应化学体系编码映射: {}", scenarioChemicalSystemCodeMap);

        // 如果映射为空，直接返回空集合
        if (scenarioChemicalSystemCodeMap == null || scenarioChemicalSystemCodeMap.isEmpty()) {
            log.warn("未找到场景ID对应化学体系编码的映射，bomCostOverviewId: {}", bomCostOverviewId);
            return new HashSet<>();
        }

        for (PositiveMaterialAccounting accounting : accountingList) {
            String chemicalSystemCode = accounting.getChemicalSystemCode();
            String chemicalManagementElementsJson = accounting.getChemicalManagementElementsJson();

            // 检查必要字段是否为空
            if (chemicalSystemCode == null || chemicalSystemCode.trim().isEmpty()) {
                log.warn("正极材料核算记录的化学体系编号为空，跳过处理: {}", accounting.getId());
                continue;
            }

            if (chemicalManagementElementsJson == null || chemicalManagementElementsJson.trim().isEmpty()) {
                log.warn("正极材料核算记录的化学管理元素JSON为空，跳过处理: {}", accounting.getId());
                continue;
            }

            // 根据化学体系编码找到对应的场景ID（一个场景ID可能包含多个化学体系编码）
            List<Long> matchingScenarioIds = scenarioChemicalSystemCodeMap.entrySet().stream()
                    .filter(entry -> entry.getValue() != null && entry.getValue().contains(chemicalSystemCode))
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

            if (matchingScenarioIds.isEmpty()) {
                log.warn("未找到化学体系编码 {} 对应的场景ID", chemicalSystemCode);
                continue;
            }

            log.info("化学体系编码 {} 对应的场景ID: {}", chemicalSystemCode, matchingScenarioIds);

            try {
                JSONObject elementsJson = JSONObject.parseObject(chemicalManagementElementsJson);

                for (Map.Entry<String, Object> entry : elementsJson.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue();

                    // 解析key格式：accountingType_elementName
                    if (!key.contains("_")) {
                        continue;
                    }

                    String[] parts = key.split("_", 2);
                    if (parts.length != 2) {
                        continue;
                    }

                    String accountingTypeStr = parts[0];
                    String elementName = parts[1];

                    // 检查核算类型是否匹配
                    if (!String.valueOf(positiveElectrodeAccountingType).equals(accountingTypeStr)) {
                        continue;
                    }

                    // 获取化学元素
                    ChemicalElement element = elementNameMap.get(accountingTypeStr + "_" + elementName);
                    if (element == null) {
                        continue;
                    }

                    // 解析使用量
                    BigDecimal usage = parseUsage(value);

                    // 只保留有使用量的元素，为匹配的场景创建记录
                    if (usage.compareTo(BigDecimal.ZERO) > 0) {
                        for (Long scenarioId : matchingScenarioIds) {
                            requiredElements.add(new ChemicalElementUsage(
                                element.getId(),
                                elementName,
                                scenarioId, // 设置对应的场景ID
                                usage
                            ));
                        }
                    }
                }
            } catch (Exception e) {
                log.error("解析化学管理元素JSON失败: {}", chemicalManagementElementsJson, e);
            }
        }

        return requiredElements;
    }

    /**
     * 根据场景ID分组执行批量删除和新增操作
     */
    private void performBatchOperations(List<ChemicalElementPrice> existingPrices,
                                      Set<ChemicalElementUsage> requiredElements,
                                      Long bomCostOverviewId,
                                      Integer positiveElectrodeAccountingType) {

        log.info("开始执行批量操作，现有价格记录: {} 条，需要的元素记录: {} 条",
                existingPrices.size(), requiredElements.size());

        // 0. 预先批量获取所有需要的化学元素单位信息，避免N+1查询
        Map<Long, String> elementUnitMap = getElementUnitsInBatch(requiredElements);
        log.info("批量获取化学元素单位信息: {} 个", elementUnitMap.size());

        // 1. 按场景ID分组现有价格数据
        Map<Long, List<ChemicalElementPrice>> existingByScenario = existingPrices.stream()
                .collect(Collectors.groupingBy(ChemicalElementPrice::getScenarioId));

        // 2. 按场景ID分组需要的元素数据
        Map<Long, List<ChemicalElementUsage>> requiredByScenario = requiredElements.stream()
                .collect(Collectors.groupingBy(ChemicalElementUsage::getScenarioId));

        // 3. 获取所有涉及的场景ID
        Set<Long> allScenarioIds = new HashSet<>();
        allScenarioIds.addAll(existingByScenario.keySet());
        allScenarioIds.addAll(requiredByScenario.keySet());

        log.info("涉及的场景ID: {}", allScenarioIds);

        // 4. 收集所有需要删除和新增的数据
        List<Long> allToDeleteIds = new ArrayList<>();
        List<ChemicalElementPrice> allToAdd = new ArrayList<>();

        // 逐个场景收集删除和新增的数据
        for (Long scenarioId : allScenarioIds) {
            log.info("开始分析场景ID: {}", scenarioId);

            List<ChemicalElementPrice> scenarioExisting = existingByScenario.getOrDefault(scenarioId, new ArrayList<>());
            List<ChemicalElementUsage> scenarioRequired = requiredByScenario.getOrDefault(scenarioId, new ArrayList<>());

            // 收集该场景需要删除的记录ID
            List<Long> scenarioDeleteIds = collectScenarioDeletes(scenarioId, scenarioExisting, scenarioRequired);
            allToDeleteIds.addAll(scenarioDeleteIds);

            // 收集该场景需要新增的记录
            List<ChemicalElementPrice> scenarioAddList = collectScenarioAdds(scenarioId, scenarioExisting, scenarioRequired,
                    bomCostOverviewId, positiveElectrodeAccountingType, elementUnitMap);
            allToAdd.addAll(scenarioAddList);

            log.info("场景ID {} 分析完成，待删除: {} 条，待新增: {} 条", scenarioId, scenarioDeleteIds.size(), scenarioAddList.size());
        }

        // 5. 执行批量删除操作
        int totalDeleted = 0;
        if (!allToDeleteIds.isEmpty()) {
            log.info("开始批量删除操作，共 {} 条记录", allToDeleteIds.size());
            boolean deleteResult = this.removeByIds(allToDeleteIds);
            if (deleteResult) {
                totalDeleted = allToDeleteIds.size();
                log.info("批量删除成功: {} 条", totalDeleted);
            } else {
                log.error("批量删除失败");
                throw new ServiceException(500, "批量删除化学元素价格记录失败");
            }
        }

        // 6. 执行批量新增操作
        int totalAdded = 0;
        if (!allToAdd.isEmpty()) {
            log.info("开始批量新增操作，共 {} 条记录", allToAdd.size());
            boolean addResult = this.saveBatch(allToAdd, 1000); // 分批保存，每批1000条
            if (addResult) {
                totalAdded = allToAdd.size();
                log.info("批量新增成功: {} 条", totalAdded);
            } else {
                log.error("批量新增失败");
                throw new ServiceException(500, "批量新增化学元素价格记录失败");
            }
        }

        log.info("批量操作完成，总删除: {} 条，总新增: {} 条", totalDeleted, totalAdded);
    }

    /**
     * 收集单个场景需要删除的记录ID
     */
    private List<Long> collectScenarioDeletes(Long scenarioId,
                                            List<ChemicalElementPrice> scenarioExisting,
                                            List<ChemicalElementUsage> scenarioRequired) {

        if (scenarioExisting.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取该场景需要的化学元素ID集合
        Set<Long> requiredElementIds = scenarioRequired.stream()
                .map(ChemicalElementUsage::getChemicalElementId)
                .collect(Collectors.toSet());

        // 找出需要删除的记录：现有的但不在需要列表中的化学元素
        List<Long> toDeleteIds = scenarioExisting.stream()
                .filter(price -> !requiredElementIds.contains(price.getChemicalElementId()))
                .map(ChemicalElementPrice::getId)
                .collect(Collectors.toList());

        log.debug("场景ID {} 收集到待删除记录: {} 条", scenarioId, toDeleteIds.size());
        return toDeleteIds;
    }

    /**
     * 处理单个场景的删除操作（已废弃，保留用于兼容性）
     * @deprecated 使用 collectScenarioDeletes 替代
     */
    @Deprecated
    private int processScenarioDeletes(Long scenarioId,
                                     List<ChemicalElementPrice> scenarioExisting,
                                     List<ChemicalElementUsage> scenarioRequired) {

        if (scenarioExisting.isEmpty()) {
            return 0;
        }

        // 获取该场景需要的化学元素ID集合
        Set<Long> requiredElementIds = scenarioRequired.stream()
                .map(ChemicalElementUsage::getChemicalElementId)
                .collect(Collectors.toSet());

        // 找出需要删除的记录：现有的但不在需要列表中的化学元素
        List<ChemicalElementPrice> toDelete = scenarioExisting.stream()
                .filter(price -> !requiredElementIds.contains(price.getChemicalElementId()))
                .collect(Collectors.toList());

        if (!toDelete.isEmpty()) {
            List<Long> deleteIds = toDelete.stream()
                    .map(ChemicalElementPrice::getId)
                    .collect(Collectors.toList());

            this.removeByIds(deleteIds);
            log.info("场景ID {} 删除化学元素价格记录: {} 条", scenarioId, deleteIds.size());
            return deleteIds.size();
        }

        return 0;
    }

    /**
     * 收集单个场景需要新增的记录
     */
    private List<ChemicalElementPrice> collectScenarioAdds(Long scenarioId,
                                                         List<ChemicalElementPrice> scenarioExisting,
                                                         List<ChemicalElementUsage> scenarioRequired,
                                                         Long bomCostOverviewId,
                                                         Integer positiveElectrodeAccountingType,
                                                         Map<Long, String> elementUnitMap) {

        if (scenarioRequired.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取该场景现有的化学元素ID集合
        Set<Long> existingElementIds = scenarioExisting.stream()
                .map(ChemicalElementPrice::getChemicalElementId)
                .collect(Collectors.toSet());

        // 找出需要新增的记录：需要的但现有列表中没有的化学元素
        List<ChemicalElementPrice> toAdd = scenarioRequired.stream()
                .filter(usage -> !existingElementIds.contains(usage.getChemicalElementId()))
                .map(usage -> ChemicalElementPrice.builder()
                        .bomCostOverviewId(bomCostOverviewId)
                        .positiveElectrodeAccountingType(positiveElectrodeAccountingType)
                        .chemicalElementId(usage.getChemicalElementId())
                        .scenarioId(scenarioId)
                        .unit(elementUnitMap.getOrDefault(usage.getChemicalElementId(), ""))
                        .cnyPrice(BigDecimal.ZERO)
                        .foreignPrice(BigDecimal.ZERO)
                        .currencyType("USD")
                        .build())
                .collect(Collectors.toList());

        log.debug("场景ID {} 收集到待新增记录: {} 条", scenarioId, toAdd.size());
        return toAdd;
    }

    /**
     * 处理单个场景的新增操作（已废弃，保留用于兼容性）
     * @deprecated 使用 collectScenarioAdds 替代
     */
    @Deprecated
    private int processScenarioAdds(Long scenarioId,
                                  List<ChemicalElementPrice> scenarioExisting,
                                  List<ChemicalElementUsage> scenarioRequired,
                                  Long bomCostOverviewId,
                                  Integer positiveElectrodeAccountingType,
                                  Map<Long, String> elementUnitMap) {

        if (scenarioRequired.isEmpty()) {
            return 0;
        }

        // 获取该场景现有的化学元素ID集合
        Set<Long> existingElementIds = scenarioExisting.stream()
                .map(ChemicalElementPrice::getChemicalElementId)
                .collect(Collectors.toSet());

        // 找出需要新增的记录：需要的但现有列表中没有的化学元素
        List<ChemicalElementPrice> toAdd = scenarioRequired.stream()
                .filter(usage -> !existingElementIds.contains(usage.getChemicalElementId()))
                .map(usage -> ChemicalElementPrice.builder()
                        .bomCostOverviewId(bomCostOverviewId)
                        .positiveElectrodeAccountingType(positiveElectrodeAccountingType)
                        .chemicalElementId(usage.getChemicalElementId())
                        .scenarioId(scenarioId)
                        .unit(elementUnitMap.getOrDefault(usage.getChemicalElementId(), ""))
                        .cnyPrice(BigDecimal.ZERO)
                        .foreignPrice(BigDecimal.ZERO)
                        .currencyType("USD")
                        .build())
                .collect(Collectors.toList());

        if (!toAdd.isEmpty()) {
            this.saveBatch(toAdd);
            log.info("场景ID {} 新增化学元素价格记录: {} 条", scenarioId, toAdd.size());
            return toAdd.size();
        }

        return 0;
    }

    /**
     * 批量获取化学元素的单位信息，避免N+1查询
     */
    private Map<Long, String> getElementUnitsInBatch(Set<ChemicalElementUsage> requiredElements) {
        // 提取所有需要的化学元素ID
        Set<Long> elementIds = requiredElements.stream()
                .map(ChemicalElementUsage::getChemicalElementId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (elementIds.isEmpty()) {
            return new HashMap<>();
        }

        // 批量查询化学元素信息
        LambdaQueryWrapper<ChemicalElement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(ChemicalElement::getId, ChemicalElement::getUnit);
        queryWrapper.in(ChemicalElement::getId, elementIds);

        List<ChemicalElement> elements = chemicalElementService.list(queryWrapper);

        // 构建ID到单位的映射
        Map<Long, String> unitMap = elements.stream()
                .collect(Collectors.toMap(
                    ChemicalElement::getId,
                    element -> element.getUnit() != null ? element.getUnit() : "",
                    (existing, replacement) -> existing // 处理重复key的情况
                ));

        log.info("批量获取化学元素单位，查询元素ID: {}，获取到单位信息: {} 个", elementIds.size(), unitMap.size());

        return unitMap;
    }



    /**
     * 化学元素使用量信息
     */
    private static class ChemicalElementUsage {
        private Long chemicalElementId;
        private Long scenarioId;

        public ChemicalElementUsage(Long chemicalElementId, String elementName, Long scenarioId, BigDecimal usage) {
            this.chemicalElementId = chemicalElementId;
            this.scenarioId = scenarioId;
        }

        // Getters
        public Long getChemicalElementId() { return chemicalElementId; }
        public Long getScenarioId() { return scenarioId; }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            ChemicalElementUsage that = (ChemicalElementUsage) o;
            return Objects.equals(chemicalElementId, that.chemicalElementId) &&
                   Objects.equals(scenarioId, that.scenarioId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(chemicalElementId, scenarioId);
        }
    }

}
