<template>
  <a-modal
    centered
    title="编辑化学元素"
    :visible="visible"
    :loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading">
      <a-form :form="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item style="display: none">
          <a-input v-decorator="['id']" />
        </a-form-item>
        
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="元素名称" required>
              <a-input
                v-decorator="['elementName', {
                  rules: [{ required: true, message: '请输入元素名称！' }]
                }]"
                placeholder="请输入元素名称"
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="24">
            <a-form-item label="核算类型" required>
              <a-select
                mode="multiple"
                v-decorator="['accountingType', {
                  rules: [{ required: true, message: '请选择核算类型！' }]
                }]"
                placeholder="请选择核算类型"
              >
               <a-select-option v-for="(item,i) in getDict('bom_bill_account_type')" :value="item.code" :key="i">
                    {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item label="单位" required>
              <a-input
                v-decorator="['unit', {
                  rules: [{ required: true, message: '请输入单位！' }]
                }]"
                placeholder="请输入单位"
              />
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item label="金属前提展示">
              <a-select
                v-decorator="['metalPremiseDisplay', {
                  initialValue: 0
                }]"
                placeholder="请选择是否金属前提展示"
              >
                <a-select-option :value="0">否</a-select-option>
                <a-select-option :value="1">是</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item label="排序">
              <a-input-number 
                placeholder="请输入排序" 
                :min="1"
                :precision="0"
                style="width: 100%"
                v-decorator="['sort', {rules: [{required: true, message: '请输入排序！'}]}]" 
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { chemicalElementEdit } from '@/api/modular/system/chemicalElementManage'
import { DICT_TYPE_TREE_DATA } from '@/store/mutation-types'
import Vue from 'vue'
export default {
  data() {
    return {
      visible: false,
      loading: false,
      form: this.$form.createForm(this)
    }
  },
  
  methods: {
    getDict(code) {
			const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
			return dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
		},
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue({
          id: record.id,
          elementName: record.elementName,
          unit: record.unit,
          // 将逗号分隔字符串转换为数组
          accountingType: record.accountingType ? record.accountingType.split(',') : [],
          metalPremiseDisplay: record.metalPremiseDisplay !== undefined ? record.metalPremiseDisplay : 0,
          sort: record.sort ? record.sort : 1
        })
      })
    },
    
    handleSubmit() {
      this.form.validateFields((errors, values) => {
        if (!errors) {
          this.loading = true
          // 将多选数组转换为逗号分隔字符串
          values.accountingType = values.accountingType.join(',')
          chemicalElementEdit(values).then(res => {
            if (res.success) {
              this.$message.success('更新成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$message.error('更新失败：' + res.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    
    handleCancel() {
      this.form.resetFields()
      this.visible = false
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep .ant-modal-footer{
 padding:0 24px 24px; 
}
.link-btn {
  margin-right: 8px;
  color: #1890FF;
}
</style>
