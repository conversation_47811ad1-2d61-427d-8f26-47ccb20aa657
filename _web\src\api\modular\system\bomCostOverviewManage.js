/**
 * BOM成本总览表管理
 *
 * <AUTHOR>
 * @date 2025年7月1日
 */
import { axios } from '@/utils/request'

/**
 * BOM成本总览表分页列表
 *
 * <AUTHOR>
 */
export function getBomCostOverviewPage(parameter) {
  return axios({
    url: '/bomCostOverview/pageList',
    method: 'post',
    data: parameter
  })
}

/**
 * BOM成本总览表列表
 *
 * <AUTHOR>
 */
export function getBomCostOverviewList(parameter) {
  return axios({
    url: '/bomCostOverview/list',
    method: 'post',
    data: parameter
  })
}

/**
 * BOM成本总览表场景分组分页查询
 * 返回场景数据，包含BOM成本总览信息
 *
 * <AUTHOR>
 */
export function getBomCostOverviewScenarioGroupPageList(parameter) {
  return axios({
    url: '/bomCostOverview/scenarioGroupPageList',
    method: 'post',
    data: parameter
  })
}

/**
 * 新增BOM成本总览表
 *
 * <AUTHOR>
 */
export function bomCostOverviewAdd(parameter) {
  return axios({
    url: '/bomCostOverview/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑BOM成本总览表
 *
 * <AUTHOR>
 */
export function bomCostOverviewEdit(parameter) {
  return axios({
    url: '/bomCostOverview/update',
    method: 'post',
    data: parameter
  })
}

export function bomCostOverviewFinish(parameter) {
  return axios({
    url: '/bomCostOverview/finish',
    method: 'post',
    data: parameter
  })
}

/**
 * 导出BOM成本总览Excel
 *
 * <AUTHOR>
 */
export function bomCostOverviewExport(parameter) {
  return axios.post('/bomCostOverview/export', {...parameter}, {responseType: 'blob'})
}

/**
 * 复制选中的BOM成本总览数据
 *
 * <AUTHOR>
 */
export function bomCostOverviewCopy(parameter) {
  return axios({
    url: '/bomCostOverview/copy',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除BOM成本总览表
 *
 * <AUTHOR>
 */
export function bomCostOverviewDelete(parameter) {
  return axios({
    url: '/bomCostOverview/delete',
    method: 'post',
    data: parameter
  })
}

/**
 * 获取BOM成本总览表详情
 *
 * <AUTHOR>
 */
export function bomCostOverviewGet(parameter) {
  return axios({
    url: '/bomCostOverview/get',
    method: 'post',
    data: parameter
  })
}

/**
 * BOM成本对比
 *
 * <AUTHOR>
 */
export function bomCostOverviewCompare(parameter) {
  return axios({
    url: '/bomCostOverview/compare',
    method: 'post',
    data: parameter
  })
}

/**
 * 计算材料成本
 *
 * <AUTHOR>
 */
export function bomCostOverviewCalculateMaterialCosts(parameter) {
  return axios({
    url: '/bomCostOverview/calculateMaterialCosts',
    method: 'post',
    data: parameter
  })
}

/**
 * 导出BOM成本对比结果
 *
 * <AUTHOR>
 */
export function bomCostOverviewExportCompare(parameter) {
  return axios({
    url: '/bomCostOverview/exportCompare',
    method: 'post',
    data: parameter,
    responseType: 'blob'
  })
}
