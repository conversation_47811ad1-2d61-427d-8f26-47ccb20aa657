package eve.sys.modular.bombill.materialprice.controller;

import eve.core.annotion.BusinessLog;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.modular.bombill.materialprice.dto.MaterialPriceExcelDto;
import eve.sys.modular.bombill.materialprice.entity.MaterialPrice;
import eve.sys.modular.bombill.materialprice.param.MaterialPriceCheckDuplicateParam;
import eve.sys.modular.bombill.materialprice.service.IMaterialPriceService;

import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/materialPrice")
public class MaterialPriceController {

    @Resource
    private IMaterialPriceService materialPriceService;

    @PostMapping("/pageList")
    @BusinessLog(title = "材料价格-分页列表查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData pageList(@RequestBody MaterialPrice param) {
        return new SuccessResponseData(materialPriceService.pageList(param));
    }

    @PostMapping("/list")
    @BusinessLog(title = "材料价格-列表查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(@RequestBody MaterialPrice param) {
        return new SuccessResponseData(materialPriceService.list(param));
    }

    @PostMapping("/get")
    @BusinessLog(title = "材料价格-根据id查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData get(@RequestBody MaterialPrice param) {
        return new SuccessResponseData(materialPriceService.get(param));
    }

    @PostMapping("/add")
    @BusinessLog(title = "材料价格-新增", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody MaterialPrice param) {
        return new SuccessResponseData(materialPriceService.add(param));
    }

    @PostMapping("/delete")
    @BusinessLog(title = "材料价格-删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody MaterialPrice param) {
        return new SuccessResponseData(materialPriceService.delete(param));
    }

    @PostMapping("/update")
    @BusinessLog(title = "材料价格-更新", opType = LogAnnotionOpTypeEnum.UPDATE)
    public ResponseData update(@RequestBody MaterialPrice param) {
        return new SuccessResponseData(materialPriceService.update(param));
    }

    @PostMapping("/export")
    @BusinessLog(title = "材料价格-导出Excel", opType = LogAnnotionOpTypeEnum.EXPORT)
    public void exportExcel(@RequestBody MaterialPrice param, HttpServletResponse response) {
        materialPriceService.exportExcel(param, response);
    }

    @PostMapping("/import")
    @BusinessLog(title = "材料价格-导入Excel", opType = LogAnnotionOpTypeEnum.IMPORT)
    public ResponseData importExcel(@RequestParam("file") MultipartFile file) {
        List<MaterialPriceExcelDto> result = materialPriceService.importExcel(file);
        return new SuccessResponseData(result);
    }

    @PostMapping("/batchSave")
    @BusinessLog(title = "材料价格-批量保存导入数据", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData batchSaveImportData(@RequestBody List<MaterialPriceExcelDto> dataList) {
        return new SuccessResponseData(materialPriceService.batchSaveImportData(dataList));
    }

    @PostMapping("/checkDuplicate")
    @BusinessLog(title = "材料价格-检查重复料号", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData checkDuplicate(@RequestBody MaterialPriceCheckDuplicateParam param) {
        List<String> duplicatePartNumbers = materialPriceService.checkDuplicatePartNumbers(param);
        return new SuccessResponseData(duplicatePartNumbers);
    }

    @PostMapping("/downloadTemplate")
    @BusinessLog(title = "材料价格-下载导入模板", opType = LogAnnotionOpTypeEnum.EXPORT)
    public void downloadTemplate(@RequestBody(required = false) MaterialPrice param, HttpServletResponse response) {
        materialPriceService.downloadTemplate(response);
    }

}