package eve.sys.modular.bombill.chemicalelementprice.param;

import lombok.Data;

/**
 * 化学元素价格重复检查参数
 *
 * <AUTHOR>
 * @date 2025年7月29日
 */
@Data
public class ChemicalElementPriceCheckDuplicateParam {

    /**
     * 场景ID
     */
    private Long scenarioId;

    /**
     * BOM成本总览表ID
     */
    private Long bomCostOverviewId;

    /**
     * 化学元素表ID
     */
    private Long chemicalElementId;

    /**
     * 正极核算类型
     */
    private String positiveElectrodeAccountingType;

    /**
     * 排除的记录ID（用于编辑时排除当前记录）
     */
    private Long excludeId;
}
