/**
 * 物料价格管理
 *
 * <AUTHOR>
 * @date 2025年X月X日
 */
import { axios } from '@/utils/request'

/**
 * 物料价格分页列表
 *
 * <AUTHOR>
 */
export function getMaterialPricePage(parameter) {
  return axios({
    url: '/materialPrice/pageList',
    method: 'post',
    data: parameter
  })
}

/**
 * 物料价格列表
 *
 * <AUTHOR>
 */
export function getMaterialPriceList(parameter) {
  return axios({
    url: '/materialPrice/list',
    method: 'post',
    data: parameter
  })
}

/**
 * 新增物料价格
 *
 * <AUTHOR>
 */
export function materialPriceAdd(parameter) {
  return axios({
    url: '/materialPrice/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑物料价格
 *
 * <AUTHOR>
 */
export function materialPriceEdit(parameter) {
  return axios({
    url: '/materialPrice/update',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除物料价格
 *
 * <AUTHOR>
 */
export function materialPriceDelete(parameter) {
  return axios({
    url: '/materialPrice/delete',
    method: 'post',
    data: parameter
  })
}

/**
 * 获取物料价格详情
 *
 * <AUTHOR>
 */
export function materialPriceGet(parameter) {
  return axios({
    url: '/materialPrice/get',
    method: 'post',
    data: parameter
  })
}

/**
 * 导出物料价格Excel
 *
 * <AUTHOR>
 */
export function materialPriceExport(parameter) {
  return axios.post('/materialPrice/export', {...parameter}, {responseType: 'blob'})
}

/**
 * 导入物料价格Excel
 *
 * <AUTHOR>
 */
export function materialPriceImport(parameter) {
  return axios({
    url: '/materialPrice/import',
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 批量保存导入的物料价格数据
 *
 * <AUTHOR>
 */
export function materialPriceBatchSave(parameter) {
  return axios({
    url: '/materialPrice/batchSave',
    method: 'post',
    data: parameter
  })
}

/**
 * 检查物料价格重复的料号
 *
 * <AUTHOR>
 */
export function materialPriceCheckDuplicate(parameter) {
  return axios({
    url: '/materialPrice/checkDuplicate',
    method: 'post',
    data: parameter
  })
}

/**
 * 下载物料价格导入模板
 *
 * <AUTHOR>
 */
export function materialPriceDownloadTemplate(parameter) {
  return axios.post('/materialPrice/downloadTemplate', {...parameter}, {responseType: 'blob'})
}