<template>
  <a-modal
    centered
    title="编辑化学元素价格"
    :visible="visible"
    :loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    width="700px"
  >
    <a-spin :spinning="loading">
      <a-form :form="form" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="场景ID" required>
              <a-input-number
                v-decorator="['scenarioId', {
                  rules: [{ required: true, message: '请输入场景ID！' }]
                }]"
                placeholder="请输入场景ID"
                style="width: 100%"
                :min="0"
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item label="BOM成本总览表ID" required>
              <a-input-number
                v-decorator="['bomCostOverviewId', {
                  rules: [{ required: true, message: '请输入BOM成本总览表ID！' }]
                }]"
                placeholder="请输入BOM成本总览表ID"
                style="width: 100%"
                :min="0"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="化学元素表ID" required>
              <a-input-number
                v-decorator="['chemicalElementId', {
                  rules: [{ required: true, message: '请输入化学元素表ID！' }]
                }]"
                placeholder="请输入化学元素表ID"
                style="width: 100%"
                :min="0"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="正极核算类型">
              <a-select
                v-decorator="['positiveElectrodeAccountingType']"
                placeholder="请选择正极核算类型"
              >
                <a-select-option :value="1">类型1</a-select-option>
                <a-select-option :value="2">类型2</a-select-option>
                <a-select-option :value="3">类型3</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="人民币价格">
              <a-input-number
                v-decorator="['cnyPrice']"
                placeholder="请输入人民币价格"
                style="width: 100%"
                :precision="2"
                :min="0"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="外币价格">
              <a-input-number
                v-decorator="['foreignPrice']"
                placeholder="请输入外币价格"
                style="width: 100%"
                :precision="2"
                :min="0"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="币种">
              <a-select
                v-decorator="['currencyType']"
                placeholder="请选择币种"
              >
                <a-select-option value="USD">美元</a-select-option>
                <a-select-option value="GBP">英镑</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="化学元素单位">
              <a-input
                v-decorator="['unit']"
                placeholder="请输入化学元素单位"
              />
            </a-form-item>
          </a-col>

        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { chemicalElementPriceEdit } from '@/api/modular/system/chemicalElementPriceManage'

export default {
  data() {
    return {
      visible: false,
      loading: false,
      form: this.$form.createForm(this)
    }
  },
  
  methods: {
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue({
          id: record.id,
          scenarioId: record.scenarioId,
          bomCostOverviewId: record.bomCostOverviewId,
          chemicalElementId: record.chemicalElementId,
          positiveElectrodeAccountingType: record.positiveElectrodeAccountingType,
          cnyPrice: record.cnyPrice,
          foreignPrice: record.foreignPrice,
          currencyType: record.currencyType || 'USD',
          unit: record.unit
        })
      })
    },
    
    handleSubmit() {
      this.form.validateFields((errors, values) => {
        if (!errors) {
          // 检查重复（排除当前记录）
          this.checkDuplicateRecord(values)
        }
      })
    },

    // 检查重复记录
    checkDuplicateRecord(values) {
      this.loading = true

      const checkParams = {
        scenarioId: values.scenarioId,
        bomCostOverviewId: values.bomCostOverviewId,
        chemicalElementId: values.chemicalElementId,
        positiveElectrodeAccountingType: values.positiveElectrodeAccountingType,
        excludeId: values.id // 排除当前记录
      }

      chemicalElementPriceCheckDuplicate(checkParams).then(res => {
        if (res.success && res.data && res.data.length > 0) {
          // 存在重复记录，显示确认对话框
          this.showDuplicateConfirmDialog(values)
        } else {
          // 没有重复，直接更新
          this.proceedWithEdit(values)
        }
      }).catch(error => {
        console.error('检查重复失败:', error)
        // 检查失败时也允许继续更新
        this.proceedWithEdit(values)
      }).finally(() => {
        this.loading = false
      })
    },

    // 显示重复确认对话框
    showDuplicateConfirmDialog(values) {
      this.$confirm({
        title: '发现重复记录',
        content: '该化学元素价格记录已存在（相同的场景ID、BOM成本总览表ID、化学元素表ID和正极核算类型）。\n\n点击"确认"将覆盖现有数据，点击"取消"将终止修改操作。',
        okText: '确认覆盖',
        cancelText: '取消修改',
        onOk: () => {
          this.proceedWithEdit(values, true)
        },
        onCancel: () => {
          this.$message.info('已取消修改操作')
        }
      })
    },

    // 执行更新
    proceedWithEdit(values, forceOverride = false) {
      this.loading = true

      const editParams = {
        ...values,
        forceOverride: forceOverride
      }

      chemicalElementPriceEdit(editParams).then(res => {
        if (res.success) {
          this.$message.success('更新成功')
          this.visible = false
          this.$emit('ok')
        } else {
          this.$message.error('更新失败：' + res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    
    handleCancel() {
      this.visible = false
      this.form.resetFields()
    }
  }
}
</script>

<style scoped>
</style>
