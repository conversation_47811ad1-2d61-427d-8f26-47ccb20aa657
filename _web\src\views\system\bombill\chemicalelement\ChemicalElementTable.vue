<template>
  <div>
    <tableIndex
		ref="pbiTableIndex"
		:pageLevel='2'
		:tableTotal='tableTotal'
		:pageTitleShow=false 
		
		:loading='loading'
		@paginationChange="handlePageChange"
		@paginationSizeChange="handlePageChange"
		@tableFocus="tableFocus"
		@tableBlur="tableBlur" 
    >
      <template #search>
        <pbiSearchContainer>
          <pbiSearchItem label='化学元素' :span="6">
            <a-input 
              size='small' 
              @keyup.enter.native='callFilter' 
              v-model='queryParam.elementName'
              placeholder='请输入化学元素'
            />
          </pbiSearchItem>
		  <pbiSearchItem label='核算类型' :span="6">
              <treeselect :limit='5' @input='callFilter' :max-height='200' style='width: 100%;' placeholder='请选择核算类型'
              value-consists-of='BRANCH_PRIORITY' v-model='queryParam.accountingTypeIds' :multiple='true'
              :options='accountingTypes' />
          </pbiSearchItem>
          
          <pbiSearchItem type='btn' :span="12">
            <div class="secondary-btn">
							<a-button class="mr10" @click="resetSearch">重置</a-button>
						</div>
            <div class="main-btn">
              <a-button type="primary" size="small" @click="callFilter" class="mr10">查询</a-button>
            </div>
            <!-- <div class="main-btn" v-if="hasPerm('chemicalElement:add')"> -->
            <div class="main-btn"">
              <a-button type="primary" size='small' @click="$refs.addChemicalElement.add()">
                新建
              </a-button>
            </div>
          </pbiSearchItem>
        </pbiSearchContainer>
      </template>
      
      <template #table>
        <ag-grid-vue 
          :style='`height: ${tableHeight}px`' 
          class='table ag-theme-balham'
		  :tooltipShowDelay="0"
          :defaultColDef="defaultColDef"
          :grid-options="gridOptions"
          :columnDefs='columnDefs'
          :rowData='rowData'
          :suppressDragLeaveHidesColumns="true"
          :suppressMoveWhenColumnDragging="true"
        />
      </template>
    </tableIndex>
    
    <addChemicalElement ref="addChemicalElement" @ok="loadData" />
    <editChemicalElement ref="editChemicalElement" @ok="loadData" />
  </div>
</template>

<script>
import { getChemicalElementPage, chemicalElementDelete } from "@/api/modular/system/chemicalElementManage"
import Vue from 'vue'
import { DICT_TYPE_TREE_DATA } from '@/store/mutation-types'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
export default {
  components:{
	Treeselect,
    addChemicalElement: () => import("./addChemicalElement"),
    editChemicalElement: () => import("./editChemicalElement"),
    accountingTypeRender:{
       template:`
          <div>{{params.onShow(params.data)}}</div>
       `
    },
	actionRender:{
        template: `
        <div>
            <div class="btns">
                <a @click="params.onEdit(params.data)">编辑</a>
                <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => params.onDel(params.data)">
                    <a>删除</a>
                </a-popconfirm>
            </div>
        </div>
        `
    },
  },
  data() {
    return {
	  tableHeight: document.documentElement.clientHeight - 40 - 16 - 100,
      pageNo: 1,
      pageSize: 20,
      loading: false,
      tableTotal: 0,
      queryParam: { elementName: '', accountingTypeIds: [] },
      rowData: [],
	  accountingTypes: [],
      defaultColDef: {
        flex: 1,
        filter: false,
        floatingFilter: false,
        editable: false
      },
      gridOptions: {},
      columnDefs: [
        {
          headerName: "序号",
          width: 70,
          field: "no",
          align: "center",
          cellRenderer: params => parseInt(params.node.id) + 1
        },
        {
          headerName: "元素名称",
          width: 150,
          field: "elementName",
          align: "center"
        },
        {
          headerName: "核算类型",
          width: 180,
          field: "accountingType",
          align: "center",
          cellRenderer: "accountingTypeRender",
           cellRendererParams:{
              onShow: this.accountingTypeShow,
           }
        },
        {
          headerName: "单位",
          width: 100,
          field: "unit",
          align: "center"
        },
        {
          headerName: '排序',
          width: 100,
          field: "sort",
          align: "center"
        },
        {
          headerName: "金属前提展示",
          width: 120,
          field: "metalPremiseDisplay",
          align: "center",
          cellRenderer: params => {
            const value = params.value
            if (value === 1) {
              return '是'
            } else if (value === 0) {
              return '否'
            }
            return '-'
          }
        },
        {
          headerName: "操作",
          width: 120,
          field: "action",
          align: "center",
          cellRenderer: "actionRender",
          cellRendererParams: {
            onEdit: this.edit,
            onDel: this.del
          }
        }
      ]
    }
  },
  
  methods: {
    resetSearch(){
      
      this.queryParam = { elementName: '', accountingTypeIds: [] }
			this.callFilter()
		},
	tableFocus() {
        this.$el.style.setProperty('--scroll-border-bottom-fixed', 'none');
        this.$el.style.setProperty('--scroll-display', 'unset');
        this.$el.style.setProperty('--scroll-border-bottom', '1px solid #Dee1e8');
    },
    // 鼠标移出
    tableBlur() {
        this.$el.style.setProperty('--scroll-border-bottom-fixed', '1px solid #dee1e8');
        this.$el.style.setProperty('--scroll-display', 'none');
        this.$el.style.setProperty('--scroll-border-bottom', 'none');
    },

	getDict(code) {
		const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
		return dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
	},
	getDictName(code,key) {
		const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
		let dict = dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
        let name = dict.find(item=>item.code == key)?.name
        return name ?? '-'
	},
	getAccountingTypes(){
        let items = this.getDict('bom_bill_account_type')
        items.sort((a,b)=>a.sort-b.sort)
        
        let options = []
        for (const item of items) {
            let _item = {
                id: parseInt(item.code),
                label: item.name
            }
            options.push(_item)
        }
        this.accountingTypes.push(...options)
    },
    callFilter() {
      this.pageNo = 1
	  this.$refs.pbiTableIndex.$refs.pbiPagination.handleWithoutChange(this.pageNo,this.pageSize)
      this.loadData()
    },
    
    handlePageChange(value) {
      this.pageNo = value.current
      this.pageSize = value.pageSize
      this.loadData()
    },
    
    loadData() {
      this.loading = true
      getChemicalElementPage({
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        ...this.queryParam
      }).then(res => {
        if (res.success) {
          this.rowData = res.data.rows
          this.tableTotal = res.data.totalRows
        }
      }).finally(() => {
        this.loading = false
      })
    },
    accountingTypeShow(record){
      let split = record.accountingType.split(',')
      split = split.map(item => this.accountingTypes.find(e => e.id == parseInt(item))?.label)
      return split.join(',')
    },
    edit(record) {
      console.log(record)
      this.$refs.editChemicalElement.edit(record)
    },
    
    del(record) {
      this.$confirm({
        title: '确认删除',
        content: '确定要删除该元素吗？',
        onOk: () => {
          this.loading = true
          chemicalElementDelete({ id: record.id }).then(res => {
            if (res.success) {
              this.$message.success('删除成功')
              this.loadData()
            } else {
              this.$message.error('删除失败：' + res.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  },
  
  created() {
    this.getAccountingTypes()
    this.loadData()
  }
}
</script>

<style lang="less" scoped="">
@import '/src/components/pageTool/style/pbiSearchItem.less';
:root {
    --scroll-display: none;
    --scroll-border-bottom: none;
    --scroll-border-bottom-fixed: '1px solid #dee1e8'; 
}
/deep/.searchItem .label{
    width: initial;
}
/deep/.ag-body-horizontal-scroll{
    border-bottom: var(--scroll-border-bottom) !important;
}
/deep/.ag-body-horizontal-scroll-viewport {
    display: var(--scroll-display) !important;
    border-bottom: var(--scroll-border-bottom) !important;
}
/deep/.ag-horizontal-left-spacer,
/deep/.ag-horizontal-right-spacer{
    border-bottom: var(--scroll-border-bottom-fixed) !important;
}
/deep/.ag-root{
    &.ag-layout-normal{
        padding: 0;
    }
}
/deep/.btns a{
    margin-right: 8px;
    color: #1890FF;
}
/deep/.element-link{
    color: #1890FF;
}
/deep/ .ant-pagination-options{
    margin-bottom:0;
}
/deep/.ant-pagination-item-link{
    display:flex;
    align-items:center;
    justify-content:center;
}
</style>