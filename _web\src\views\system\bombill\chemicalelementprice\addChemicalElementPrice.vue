<template>
  <a-modal
    centered
    title="新增化学元素价格"
    :visible="visible"
    :loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    width="700px"
  >
    <a-spin :spinning="loading">
      <a-form :form="form" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="场景ID" required>
              <a-input-number
                v-decorator="['scenarioId', {
                  rules: [{ required: true, message: '请输入场景ID！' }]
                }]"
                placeholder="请输入场景ID"
                style="width: 100%"
                :min="0"
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item label="BOM成本总览表ID" required>
              <a-input-number
                v-decorator="['bomCostOverviewId', {
                  rules: [{ required: true, message: '请输入BOM成本总览表ID！' }]
                }]"
                placeholder="请输入BOM成本总览表ID"
                style="width: 100%"
                :min="0"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="化学元素表ID" required>
              <a-input-number
                v-decorator="['chemicalElementId', {
                  rules: [{ required: true, message: '请输入化学元素表ID！' }]
                }]"
                placeholder="请输入化学元素表ID"
                style="width: 100%"
                :min="0"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="正极核算类型">
              <a-select
                v-decorator="['positiveElectrodeAccountingType']"
                placeholder="请选择正极核算类型"
              >
                <a-select-option :value="1">类型1</a-select-option>
                <a-select-option :value="2">类型2</a-select-option>
                <a-select-option :value="3">类型3</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="人民币价格">
              <a-input-number
                v-decorator="['cnyPrice']"
                placeholder="请输入人民币价格"
                style="width: 100%"
                :precision="2"
                :min="0"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="外币价格">
              <a-input-number
                v-decorator="['foreignPrice']"
                placeholder="请输入外币价格"
                style="width: 100%"
                :precision="2"
                :min="0"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="币种">
              <a-select
                v-decorator="['currencyType']"
                placeholder="请选择币种"
              >
                <a-select-option value="USD">美元</a-select-option>
                <a-select-option value="GBP">英镑</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="化学元素单位">
              <a-input
                v-decorator="['unit']"
                placeholder="请输入化学元素单位"
              />
            </a-form-item>
          </a-col>

        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { chemicalElementPriceAdd } from '@/api/modular/system/chemicalElementPriceManage'

export default {
  data() {
    return {
      visible: false,
      loading: false,
      form: this.$form.createForm(this)
    }
  },
  
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.form.resetFields()
      })
    },
    
    handleSubmit() {
      this.form.validateFields((errors, values) => {
        if (!errors) {
          this.loading = true
          chemicalElementPriceAdd(values).then(res => {
            if (res.success) {
              this.$message.success('添加成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$message.error('添加失败：' + res.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    
    handleCancel() {
      this.visible = false
      this.form.resetFields()
    }
  }
}
</script>

<style scoped>
</style>
