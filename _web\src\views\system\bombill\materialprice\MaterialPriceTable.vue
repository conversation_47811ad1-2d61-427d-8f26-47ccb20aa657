<template>
  <div>
    <tableIndex
      ref="pbiTableIndex"
      :pageLevel='2'
      :tableTotal='tableTotal'
      :pageTitleShow=false 
      :loading='loading'
      @paginationChange="handlePageChange"
      @paginationSizeChange="handlePageChange"
      @tableFocus="tableFocus"
      @tableBlur="tableBlur" 
    >
      <template #search>
        <pbiSearchContainer>
          <!-- 料号搜索框 -->
          <pbiSearchItem label='料号' :span="6">
            <a-input 
              size='small' 
              @keyup.enter.native='callFilter' 
              v-model='queryParam.partNumber'
              placeholder='请输入料号'
            />
          </pbiSearchItem>
          
          <!-- 物料规格描述搜索框 -->
          <pbiSearchItem label='物料规格' :span="6">
            <a-input 
              size='small' 
              @keyup.enter.native='callFilter' 
              v-model='queryParam.description'
              placeholder='请输入物料规格描述'
            />
          </pbiSearchItem>
          
          <!-- 材料类型多选下拉框 -->
          <pbiSearchItem label='材料类型' :span="6">
            <treeselect 
              :limit='1' 
              @input='callFilter' 
              :max-height='200' 
              style='width: 100%;' 
              placeholder='请选择材料类型'
              value-consists-of='BRANCH_PRIORITY' 
              v-model='queryParam.materialTypeIds' 
              :multiple='true'
              :options='materialTypes' 
            />
          </pbiSearchItem>
          
          <pbiSearchItem type='btn' :span="6">
            <div class="secondary-btn">
							<a-button class="mr10" @click="resetSearch">重置</a-button>
						</div>
            <div class="main-btn">
              <a-button type="primary" size="small" @click="callFilter" class="mr10">查询</a-button>
            </div>
            <!-- <div class="main-btn" v-if="hasPerm('materialPrice:add')"> -->
            <div class="main-btn">
              <a-button class="mr10" type="primary" size='small' @click="$refs.addMaterialPrice.add()">
                新建物料价格
              </a-button>
            </div>
            <div class="main-btn">
              <a-button type="default" class="mr10" size='small' @click="handleExport" :loading="exportLoading">
                <a-icon type="download" /> 
                导出
              </a-button>
            </div>
            <div class="main-btn" >
              <a-button type="default" size='small' @click="handleImport">
                <a-icon type="upload" />
                导入
              </a-button>
            </div>
          </pbiSearchItem>
        </pbiSearchContainer>
      </template>
      
      <template #table>
        <ag-grid-vue 
          :style='`height: ${tableHeight}px`' 
          class='table ag-theme-balham'
          :tooltipShowDelay="0"
          :defaultColDef="defaultColDef"
          :grid-options="gridOptions"
          :columnDefs='columnDefs'
          :rowData='rowData'
          :suppressDragLeaveHidesColumns="true"
          :suppressMoveWhenColumnDragging="true"
        />
      </template>
    </tableIndex>
    
    <addMaterialPrice ref="addMaterialPrice" @ok="loadData" />
    <editMaterialPrice ref="editMaterialPrice" @ok="loadData" />
    <ImportMaterialPriceModal ref="importModal" @ok="loadData" />
  </div>
</template>

<script>
import { getMaterialPricePage, materialPriceDelete, materialPriceExport } from "@/api/modular/system/materialPriceManage"
import Vue from 'vue'
import { DICT_TYPE_TREE_DATA } from '@/store/mutation-types'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  components: {
    Treeselect,
    addMaterialPrice: () => import("./addMaterialPrice"),
    editMaterialPrice: () => import("./editMaterialPrice"),
    ImportMaterialPriceModal: () => import("./ImportMaterialPriceModal"),
    actionRender: {
      template: `
        <div>
          <div class="btns">
            <a @click="params.onEdit(params.data)">编辑</a>
            <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => params.onDel(params.data)">
              <a>删除</a>
            </a-popconfirm>
          </div>
        </div>
      `
    },
  },
  data() {
    return {
      tableHeight: document.documentElement.clientHeight - 40 - 16 -100,
      pageNo: 1,
      pageSize: 20,
      loading: false,
      exportLoading: false,
      tableTotal: 0,
      queryParam: { 
        partNumber: '', 
        description: '', 
        materialTypeIds: [] 
      },
      rowData: [],
      materialTypes: [],
      defaultColDef: {
        flex: 1,
        filter: false,
        floatingFilter: false,
        editable: false
      },
      gridOptions: {},
      columnDefs: [
        {
          headerName: "序号",
          width: 70,
          field: "no",
          align: "center",
          cellRenderer: params => parseInt(params.node.id) + 1
        },
        {
          headerName: "材料类型",
          width: 120,
          field: "materialType",
          align: "center",
          cellRenderer: this.materialTypeCellRenderer,
        },
        {
          headerName: "料号",
          width: 150,
          field: "partNumber",
          align: "center"
        },
        {
          headerName: "物料规格",
          width: 200,
          field: "description",
          align: "center"
        },

        {
          headerName: "结构件",
          width: 100,
          field: "structuralPart",
          align: "center",
          cellRenderer: function (params) {
              return params.value == 1 ? '是' : '否'
          },
        },
        
        {
          headerName: "核算价格(未税)",
          width: 120,
          field: "accountingPrice",
          align: "center"
        },
        {
          headerName: "单位",
          width: 100,
          field: "unit",
          align: "center"
        },
        {
          headerName: "备注",
          width: 150,
          field: "remark"
        },
        {
          headerName: "操作",
          width: 120,
          field: "action",
          align: "center",
          cellRenderer: "actionRender",
          cellRendererParams: { 
            onEdit: this.edit,
            onDel: this.del
          }
        }
      ]
    }
  },
  
  methods: {
    resetSearch(){
      this.queryParam = { 
        partNumber: '', 
        description: '', 
        materialTypeIds: [] 
      }
			this.callFilter()
		},
    getDict(code) {
      const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
      return dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
    },

    getDictName(code,key) {
      const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
      let dict = dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
          let name = dict.find(item=>item.code == key)?.name
          return name ?? '-'
    },
    materialTypeCellRenderer(record){
        return this.getDictName('bom_bill_material_type',record.value+'')
    },
    
    getMaterialTypes() {
      let items = this.getDict('bom_bill_material_type') // 假设字典编码为'bom_bill_material_type'
      items.sort((a, b) => a.sort - b.sort)
      
      let options = []
      for (const item of items) {
        let _item = {
          id: parseInt(item.code),
          label: item.name
        }
        options.push(_item)
      }
      this.materialTypes = options
    },
    
    callFilter() {
      this.pageNo = 1
       this.$refs.pbiTableIndex.$refs.pbiPagination.handleWithoutChange(this.pageNo,this.pageSize)
      this.loadData()
    },
    
    handlePageChange(value) {
      this.pageNo = value.current
      this.pageSize = value.pageSize
      this.loadData()
    },
    
    loadData() {
      this.loading = true
      getMaterialPricePage({
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        ...this.queryParam
      }).then(res => {
        if (res.success) {
          this.rowData = res.data.rows
          this.tableTotal = res.data.totalRows
        }
      }).finally(() => {
        this.loading = false
      })
    },
    
    edit(record) {
      this.$refs.editMaterialPrice.edit(record)
    },
    
    del(record) {
      this.$confirm({
        title: '确认删除',
        content: '确定要删除该物料价格吗？',
        onOk: () => {
          this.loading = true
          materialPriceDelete({ id: record.id }).then(res => {
            if (res.success) {
              this.$message.success('删除成功')
              this.loadData()
            } else {
              this.$message.error('删除失败：' + res.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },

    // 导出Excel - 参考 testfee/index.vue 的 download 方法
    handleExport() {
      this.exportLoading = true
      materialPriceExport({
        ...this.queryParam,
        pageNo: this.pageNo,
        pageSize: this.pageSize
      }).then(res => {
        // 生成文件名
        const now = new Date()
        const timestamp = now.getFullYear() +
          String(now.getMonth() + 1).padStart(2, '0') +
          String(now.getDate()).padStart(2, '0') + '_' +
          String(now.getHours()).padStart(2, '0') +
          String(now.getMinutes()).padStart(2, '0') +
          String(now.getSeconds()).padStart(2, '0')

        const fileName = `材料价格一览表_${timestamp}.xlsx`
        const _res = res.data
        let blob = new Blob([_res])
        let downloadElement = document.createElement("a")
        //创建下载的链接
        let href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        //下载后文件名
        downloadElement.download = fileName
        document.body.appendChild(downloadElement)
        //点击下载
        downloadElement.click()
        //下载完成移除元素
        document.body.removeChild(downloadElement)
        //释放掉blob对象
        window.URL.revokeObjectURL(href)
        this.exportLoading = false
      }).catch(error => {
        console.error('导出失败:', error)
        this.$message.error('导出失败，请重试')
        this.exportLoading = false
      })
    },

    // 导入Excel
    handleImport() {
      this.$refs.importModal.show()
    }
  },
  
  created() {
    this.getMaterialTypes()
    this.loadData()
  }
}
</script>

<style lang="less" scoped="">
@import '/src/components/pageTool/style/pbiSearchItem.less';
:root {
    --scroll-display: none;
    --scroll-border-bottom: none;
    --scroll-border-bottom-fixed: '1px solid #dee1e8'; 
}
/deep/.searchItem .label{
    width: initial;
}
/deep/.ag-body-horizontal-scroll{
    border-bottom: var(--scroll-border-bottom) !important;
}
/deep/.ag-body-horizontal-scroll-viewport {
    display: var(--scroll-display) !important;
    border-bottom: var(--scroll-border-bottom) !important;
}
/deep/.ag-horizontal-left-spacer,
/deep/.ag-horizontal-right-spacer{
    border-bottom: var(--scroll-border-bottom-fixed) !important;
}
/deep/.ag-root{
    &.ag-layout-normal{
        padding: 0;
    }
}
/deep/.btns a{
    margin-right: 8px;
    color: #1890FF;
}
/deep/.element-link{
    color: #1890FF;
}
/deep/ .ant-pagination-options{
    margin-bottom:0;
}
/deep/.ant-pagination-item-link{
    display:flex;
    align-items:center;
    justify-content:center;
}
</style>