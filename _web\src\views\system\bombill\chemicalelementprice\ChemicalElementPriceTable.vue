<template>
  <div>
    <tableIndex
      ref="pbiTableIndex"
      :pageLevel='2'
      :tableTotal='tableTotal'
      :pageTitleShow=false 
      :loading='loading'
      @paginationChange="handlePageChange"
      @paginationSizeChange="handlePageChange"
      @tableFocus="tableFocus"
      @tableBlur="tableBlur" 
    >
      <template #search>
        <pbiSearchContainer>
          <pbiSearchItem label='场景ID' :span="6">
            <a-input-number 
              size='small' 
              @pressEnter='callFilter' 
              v-model='queryParam.scenarioId'
              placeholder='请输入场景ID'
              style="width: 100%"
              :min="0"
            />
          </pbiSearchItem>
          <pbiSearchItem label='BOM成本总览表ID' :span="6">
            <a-input-number 
              size='small' 
              @pressEnter='callFilter' 
              v-model='queryParam.bomCostOverviewId'
              placeholder='请输入BOM成本总览表ID'
              style="width: 100%"
              :min="0"
            />
          </pbiSearchItem>
          <pbiSearchItem label='化学元素表ID' :span="6">
            <a-input-number 
              size='small' 
              @pressEnter='callFilter' 
              v-model='queryParam.chemicalElementId'
              placeholder='请输入化学元素表ID'
              style="width: 100%"
              :min="0"
            />
          </pbiSearchItem>
          <pbiSearchItem label='正极核算类型' :span="6">
            <a-select
              size='small'
              @change='callFilter'
              v-model='queryParam.positiveElectrodeAccountingType'
              placeholder='请选择正极核算类型'
              allowClear
            >
              <a-select-option :value="1">类型1</a-select-option>
              <a-select-option :value="2">类型2</a-select-option>
              <a-select-option :value="3">类型3</a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem label='化学元素单位' :span="6">
            <a-input
              size='small'
              @keyup.enter.native='callFilter'
              v-model='queryParam.unit'
              placeholder='请输入化学元素单位'
            />
          </pbiSearchItem>
          
          <pbiSearchItem type='btn' :span="12">
            <div class="main-btn">
              <a-button type="primary" size="small" @click="callFilter" class="mr10">查询</a-button>
            </div>
            <div class="main-btn" >
              <a-button type="primary" size='small' @click="$refs.addChemicalElementPrice.add()">
                新建
              </a-button>
            </div>
          </pbiSearchItem>
        </pbiSearchContainer>
      </template>
      
      <template #table>
        <ag-grid-vue 
          :style='`height: ${tableHeight}px`' 
          class='table ag-theme-balham'
          :tooltipShowDelay="0"
          :defaultColDef="defaultColDef"
          :grid-options="gridOptions"
          :columnDefs='columnDefs'
          :rowData='rowData'
          :suppressDragLeaveHidesColumns="true"
          :suppressMoveWhenColumnDragging="true"
        />
      </template>
    </tableIndex>
    
    <addChemicalElementPrice ref="addChemicalElementPrice" @ok="loadData" />
    <editChemicalElementPrice ref="editChemicalElementPrice" @ok="loadData" />
  </div>
</template>

<script>
import { getChemicalElementPricePage, chemicalElementPriceDelete } from "@/api/modular/system/chemicalElementPriceManage"

export default {
  components:{
    addChemicalElementPrice: () => import("./addChemicalElementPrice"),
    editChemicalElementPrice: () => import("./editChemicalElementPrice"),
    actionRender:{
        template: `
        <div>
            <div class="btns">
                <a @click="params.onEdit(params.data)">编辑</a>
                <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => params.onDel(params.data)">
                    <a>删除</a>
                </a-popconfirm>
            </div>
        </div>
        `
    },
  },
  data() {
    return {
      tableHeight: document.documentElement.clientHeight - 40 - 16 - 100,
      pageNo: 1,
      pageSize: 20,
      loading: false,
      tableTotal: 0,
      queryParam: {
        scenarioId: undefined,
        bomCostOverviewId: undefined,
        chemicalElementId: undefined,
        positiveElectrodeAccountingType: undefined,
        unit: ''
      },
      rowData: [],
      defaultColDef: {
        flex: 1,
        filter: false,
        floatingFilter: false,
        editable: false
      },
      gridOptions: {},
      columnDefs: [
        {
          headerName: "序号",
          width: 70,
          field: "no",
          align: "center",
          cellRenderer: params => parseInt(params.node.id) + 1
        },
        {
          headerName: "场景ID",
          width: 100,
          field: "scenarioId",
          align: "center"
        },
        {
          headerName: "BOM成本总览表ID",
          width: 150,
          field: "bomCostOverviewId",
          align: "center"
        },
        {
          headerName: "化学元素表ID",
          width: 130,
          field: "chemicalElementId",
          align: "center"
        },
        {
          headerName: "正极核算类型",
          width: 120,
          field: "positiveElectrodeAccountingType",
          align: "center",
          cellRenderer: params => {
            const typeMap = { 1: '类型1', 2: '类型2', 3: '类型3' }
            return typeMap[params.value] || '-'
          }
        },
        {
          headerName: "人民币价格",
          width: 120,
          field: "cnyPrice",
          align: "center",
          cellRenderer: params => {
            if (params.value !== null && params.value !== undefined) {
              return `¥${Number(params.value).toFixed(2)}`
            }
            return '-'
          }
        },
        {
          headerName: "外币价格",
          width: 120,
          field: "foreignPrice",
          align: "center",
          cellRenderer: params => {
            if (params.value !== null && params.value !== undefined) {
              return `$${Number(params.value).toFixed(2)}`
            }
            return '-'
          }
        },
        {
          headerName: "币种",
          width: 80,
          field: "currencyType",
          align: "center",
          cellRenderer: params => {
            const currencyMap = { USD: '美元', GBP: '英镑' }
            return currencyMap[params.value] || params.value || '-'
          }
        },
        {
          headerName: "化学元素单位",
          width: 120,
          field: "unit",
          align: "center"
        },
        {
          headerName: "创建时间",
          width: 150,
          field: "createTime",
          align: "center",
          cellRenderer: params => {
            if (params.value) {
              return new Date(params.value).toLocaleString('zh-CN')
            }
            return '-'
          }
        },
        {
          headerName: "操作",
          width: 120,
          field: "action",
          align: "center",
          cellRenderer: "actionRender",
          cellRendererParams: { 
            onEdit: this.edit,
            onDel: this.del
          }
        }
      ]
    }
  },
  
  methods: {
    tableFocus() {
        this.$el.style.setProperty('--scroll-border-bottom-fixed', 'none');
        this.$el.style.setProperty('--scroll-display', 'unset');
        this.$el.style.setProperty('--scroll-border-bottom', '1px solid #Dee1e8');
    },
    // 鼠标移出
    tableBlur() {
        this.$el.style.setProperty('--scroll-border-bottom-fixed', '1px solid #dee1e8');
        this.$el.style.setProperty('--scroll-display', 'none');
        this.$el.style.setProperty('--scroll-border-bottom', 'none');
    },

    callFilter() {
      this.pageNo = 1
      this.$refs.pbiTableIndex.$refs.pbiPagination.handleWithoutChange(this.pageNo,this.pageSize)
      this.loadData()
    },
    
    handlePageChange(value) {
      this.pageNo = value.current
      this.pageSize = value.pageSize
      this.loadData()
    },
    
    loadData() {
      this.loading = true
      getChemicalElementPricePage({
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        ...this.queryParam
      }).then(res => {
        if (res.success) {
          this.rowData = res.data.rows
          this.tableTotal = res.data.totalRows
        }
      }).finally(() => {
        this.loading = false
      })
    },
    
    edit(record) {
      console.log(record)
      this.$refs.editChemicalElementPrice.edit(record)
    },
    
    del(record) {
      this.$confirm({
        title: '确认删除',
        content: '确定要删除该化学元素价格记录吗？',
        onOk: () => {
          this.loading = true
          chemicalElementPriceDelete({ id: record.id }).then(res => {
            if (res.success) {
              this.$message.success('删除成功')
              this.loadData()
            } else {
              this.$message.error('删除失败：' + res.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  },
  
  created() {
    this.loadData()
  }
}
</script>

<style lang="less" scoped="">
:root {
    --scroll-display: none;
    --scroll-border-bottom: none;
    --scroll-border-bottom-fixed: '1px solid #dee1e8'; 
}
/deep/.searchItem .label{
    width: initial;
}
/deep/.ag-body-horizontal-scroll{
    border-bottom: var(--scroll-border-bottom) !important;
}
/deep/.ag-body-horizontal-scroll-viewport {
    display: var(--scroll-display) !important;
    border-bottom: var(--scroll-border-bottom) !important;
}
/deep/.ag-horizontal-left-spacer,
/deep/.ag-horizontal-right-spacer{
    border-bottom: var(--scroll-border-bottom-fixed) !important;
}
/deep/.ag-root{
    &.ag-layout-normal{
        padding: 0;
    }
}
/deep/.btns a{
    margin-right: 8px;
    color: #1890FF;
}
/deep/ .ant-pagination-options{
    margin-bottom:0;
}
/deep/.ant-pagination-item-link{
    display:flex;
    align-items:center;
    justify-content:center;
}
</style>
