package eve.sys.modular.bombill.chemicalelementprice.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import eve.core.pojo.base.entity.BaseEntity;
import lombok.*;

/**
 * 化学元素价格实体类
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("CHEMICAL_ELEMENT_PRICE")
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ChemicalElementPrice extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 场景ID
     */
    private Long scenarioId;

    /**
     * BOM成本总览表ID
     */
    private Long bomCostOverviewId;

    /**
     * 化学元素表ID
     */
    private Long chemicalElementId;

    /**
     * 正极核算类型
     */
    private Integer positiveElectrodeAccountingType;

    /**
     * 人民币价格
     */
    private BigDecimal cnyPrice;

    /**
     * 外币价格
     */
    private BigDecimal foreignPrice;

    /**
     * 汇率币种（USD/GBP）
     */
    private String currencyType;

    /**
     * 化学元素单位
     */
    private String unit;

    /**
     * 化学元素名称（关联查询结果）
     */
    @TableField(exist = false)
    private String elementName;
}
