package eve.sys.modular.bombill.materialprice.service;

import com.baomidou.mybatisplus.extension.service.IService;
import eve.core.pojo.page.PageResult;
import eve.sys.modular.bombill.materialprice.dto.MaterialPriceExcelDto;
import eve.sys.modular.bombill.materialprice.entity.MaterialPrice;
import eve.sys.modular.bombill.materialprice.param.MaterialPriceCheckDuplicateParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface IMaterialPriceService extends IService<MaterialPrice> {
    PageResult<MaterialPrice> pageList(MaterialPrice param);
    List<MaterialPrice> list(MaterialPrice param);
    Boolean add(MaterialPrice param);
    Boolean delete(MaterialPrice param);
    Boolean update(MaterialPrice param);
    MaterialPrice get(MaterialPrice param);

    /**
     * 导出Excel
     */
    void exportExcel(MaterialPrice param, HttpServletResponse response);

    /**
     * 导入Excel
     */
    List<MaterialPriceExcelDto> importExcel(MultipartFile file);

    /**
     * 批量保存导入的数据
     */
    Boolean batchSaveImportData(List<MaterialPriceExcelDto> dataList);

    /**
     * 下载导入模板
     */
    void downloadTemplate(HttpServletResponse response);

    /**
     * 检查重复的料号
     */
    List<String> checkDuplicatePartNumbers(MaterialPriceCheckDuplicateParam param);
}