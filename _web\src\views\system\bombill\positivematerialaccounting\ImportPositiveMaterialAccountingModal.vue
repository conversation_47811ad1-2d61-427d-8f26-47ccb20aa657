<template>
  <a-modal
    title="导入正极材料核算"
    :width="1200"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
  >
  <template #footer><div></div></template>
    <div>
      <!-- 文件上传区域 -->
      <div class="upload-section" v-if="!importData.length">
        <a-upload-dragger
          :fileList="fileList"
          :beforeUpload="beforeUpload"
          :remove="handleRemove"
          accept=".xlsx,.xls"
        >
          <p class="ant-upload-drag-icon">
            <a-icon type="inbox" />
          </p>
          <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p class="ant-upload-hint">
            支持单个文件上传，仅支持 .xlsx 和 .xls 格式
          </p>
        </a-upload-dragger>

        <div class="upload-actions" style="margin-top: 16px;">
          <a-button
            type="primary"
            :loading="uploadLoading"
            :disabled="!fileList.length"
            @click="handleUpload"
          >
            解析文件
          </a-button>
          <a-button style="margin-left: 8px;" @click="downloadTemplate">
            下载模板
          </a-button>
        </div>
      </div>

      <!-- 数据预览区域 -->
      <div v-if="importData.length">
        <div class="import-summary">
          <a-alert
            :message="`共解析 ${importData.length} 条数据，其中 ${validCount} 条有效，${errorCount} 条有错误`"
            :type="errorCount > 0 ? 'warning' : 'success'"
            show-icon
            style="margin-bottom: 16px;"
          />
        </div>

        <ag-grid-vue
          style="height: 400px;"
          class="ag-theme-balham"
          :columnDefs="columnDefs"
          :rowData="importData"
          :defaultColDef="defaultColDef"
          :suppressDragLeaveHidesColumns="true"
          :suppressMoveWhenColumnDragging="true"
        />

        <div class="import-actions" style="margin-top: 16px;">
          <a-button @click="resetImport">重新上传</a-button>
          <a-button
            type="primary"
            :disabled="validCount === 0"
            style="margin-left: 8px;"
            @click="handleImport"
          >
            导入有效数据 ({{ validCount }} 条)
          </a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script>
import {
  positiveMaterialAccountingImportExcel,
  positiveMaterialAccountingBatchSaveImportData,
  positiveMaterialAccountingDownloadTemplate,
  positiveMaterialAccountingCheckDuplicate
} from '@/api/modular/system/positiveMaterialAccountingManage'
import { getChemicalElementList } from "@/api/modular/system/chemicalElementManage"
import Vue from 'vue'
import { DICT_TYPE_TREE_DATA } from '@/store/mutation-types'

export default {
  name: 'ImportPositiveMaterialAccountingModal',
  data() {
    return {
      visible: false,
      confirmLoading: false,
      uploadLoading: false,
      fileList: [],
      importData: [],
      chemicalElements: [], // 化学元素列表
      defaultColDef: {
        flex: 1,
        filter: false,
        floatingFilter: false,
        editable: false,
        resizable: true
      },
      columnDefs: [] // 动态生成列定义
    }
  },
  computed: {
    validCount() {
      return this.importData.filter(item => !item.errorMessage).length
    },
    errorCount() {
      return this.importData.filter(item => item.errorMessage).length
    }
  },
  methods: {
    show() {
      this.visible = true
      this.resetImport()
      this.loadChemicalElements()
    },

    handleCancel() {
      this.visible = false
      this.resetImport()
    },

    handleOk() {
      this.handleCancel()
    },

    resetImport() {
      this.fileList = []
      this.importData = []
      this.confirmLoading = false
      this.uploadLoading = false
    },

    beforeUpload(file) {
      this.fileList = [file]
      return false // 阻止自动上传
    },

    handleRemove() {
      this.fileList = []
    },

    // 下载模板
    downloadTemplate() {
      positiveMaterialAccountingDownloadTemplate().then(res => {
        const now = new Date()
        const timestamp = now.getFullYear() + 
          String(now.getMonth() + 1).padStart(2, '0') + 
          String(now.getDate()).padStart(2, '0') + '_' +
          String(now.getHours()).padStart(2, '0') + 
          String(now.getMinutes()).padStart(2, '0') + 
          String(now.getSeconds()).padStart(2, '0')
        
        const fileName = `正极材料核算导入模板_${timestamp}.xlsx`
        const _res = res.data
        let blob = new Blob([_res])
        let downloadElement = document.createElement("a")
        let href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        downloadElement.download = fileName
        document.body.appendChild(downloadElement)
        downloadElement.click()
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)
      }).catch(error => {
        console.error('模板下载失败:', error)
        this.$message.error('模板下载失败，请重试')
      })
    },

    // 解析文件
    handleUpload() {
      if (!this.fileList.length) {
        this.$message.warning('请先选择文件')
        return
      }

      this.uploadLoading = true
      positiveMaterialAccountingImportExcel(this.fileList[0]).then(res => {
        if (res.success) {
          this.importData = res.data || []
          // 重新生成列定义以确保显示所有动态列
          this.generateColumnDefs()
          this.$message.success(`文件解析成功，共解析 ${this.importData.length} 条数据`)
        } else {
          this.$message.error(res.message || '文件解析失败')
        }
      }).catch(error => {
        console.error('文件解析失败:', error)
        this.$message.error('文件解析失败，请检查文件格式')
      }).finally(() => {
        this.uploadLoading = false
      })
    },

    // 导入数据
    handleImport() {
      const validData = this.importData.filter(item => !item.errorMessage)

      if (validData.length === 0) {
        this.$message.warning('没有有效数据可以导入')
        return
      }

      // 检查是否存在相同的正极体系编号
      this.checkDuplicateSystemCodes(validData)
    },

    // 检查重复的正极体系编号
    checkDuplicateSystemCodes(validData) {
      // 提取所有正极体系编号
      const systemCodes = validData.map(item => item.chemicalSystemCode).filter(code => code)

      if (systemCodes.length === 0) {
        this.proceedWithImport(validData)
        return
      }

      // 调用后端API检查重复
      this.confirmLoading = true
      positiveMaterialAccountingCheckDuplicate({ systemCodes }).then(res => {
        if (res.success && res.data && res.data.length > 0) {
          // 存在重复的正极体系编号，显示确认对话框
          const duplicateCodes = res.data
          this.showDuplicateConfirmDialog(duplicateCodes, validData)
        } else {
          // 没有重复，直接导入
          this.proceedWithImport(validData)
        }
      }).catch(error => {
        console.error('检查重复失败:', error)
        // 检查失败时也允许继续导入
        this.proceedWithImport(validData)
      }).finally(() => {
        this.confirmLoading = false
      })
    },

    // 显示重复确认对话框
    showDuplicateConfirmDialog(duplicateCodes, validData) {
      const duplicateList = duplicateCodes.join('、')

      this.$confirm({
        title: '发现重复的正极体系编号',
        content: `以下正极体系编号在数据库中已存在：${duplicateList}。\n\n点击"确认"将覆盖现有数据，点击"取消"将终止导入操作。`,
        okText: '确认覆盖',
        cancelText: '取消导入',
        onOk: () => {
          this.proceedWithImport(validData, true)
        },
        onCancel: () => {
          this.$message.info('已取消导入操作')
        }
      })
    },

    // 执行导入
    proceedWithImport(validData, forceOverride = false) {
      this.confirmLoading = true

      const importParams = {
        data: validData,
        forceOverride: forceOverride
      }

      positiveMaterialAccountingBatchSaveImportData(importParams).then(res => {
        if (res.success) {
          this.$message.success(`成功导入 ${validData.length} 条数据`)
          this.$emit('ok')
          this.handleCancel()
        } else {
          // 显示详细的错误信息
          this.showDetailedError(res.message || '导入失败')
        }
      }).catch(error => {
        console.error('导入失败:', error)
        // 处理网络错误或其他异常
        let errorMessage = '导入失败，请重试'
        if (error.response && error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message
        } else if (error.message) {
          errorMessage = error.message
        }
        this.showDetailedError(errorMessage)
      }).finally(() => {
        this.confirmLoading = false
      })
    },

    // 加载化学元素列表
    loadChemicalElements() {
      getChemicalElementList({}).then(res => {
        if (res.success) {
          this.chemicalElements = res.data || []
          this.generateColumnDefs()
        } else {
          this.$message.error('获取化学元素列表失败')
        }
      }).catch(error => {
        console.error('获取化学元素列表失败:', error)
        this.$message.error('获取化学元素列表失败')
      })
    },

    // 生成动态列定义
    generateColumnDefs() {
      const columnDefs = [
        {
          headerName: '正极体系编号',
          field: 'chemicalSystemCode',
          width: 150,
          pinned: 'left'
        },
        {
          headerName: '正极体系',
          field: 'chemicalSystem',
          width: 120,
          pinned: 'left'
        },
        {
          headerName: '加工费',
          field: 'processingFee',
          width: 100,
          pinned: 'left'
        }
      ]

      // 按核算类型分组化学元素
      const groupByAccountingType = {}
      this.chemicalElements.forEach(element => {
        if (!groupByAccountingType[element.accountingType]) {
          groupByAccountingType[element.accountingType] = []
        }
        groupByAccountingType[element.accountingType].push(element)
      })

      // 按核算类型排序，生成分组表头
      Object.keys(groupByAccountingType).sort().forEach(accountingType => {
        const elements = groupByAccountingType[accountingType]

        if (elements && elements.length > 0) {
          const children = []

          elements.forEach(element => {
            const key = element.accountingType + '_' + element.elementName
            children.push({
              headerName: element.elementName,
              field: key,
              minWidth: 100,
              flex: 1,
              align: "center",
              valueGetter: params => {
                if (params.data && params.data.elementData) {
                  return params.data.elementData[key] || ''
                }
                return ''
              }
            })
          })

          // 添加分组表头
          columnDefs.push({
            headerName: `每KG正极${this.getDictName('bom_bill_account_type', accountingType)}用量(kg)`,
            children: children
          })
        }
      })

      // 添加错误信息列
      columnDefs.push({
        headerName: '错误信息',
        field: 'errorMessage',
        minWidth: 200,
        cellStyle: params => {
          if (params.value) {
            return { color: 'red', fontWeight: 'bold' }
          }
          return null
        }
      })

      this.columnDefs = columnDefs
    },

    // 获取字典名称
    getDictName(code, key) {
      const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
      let dict = dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
      let name = dict.find(item => item.code == key)?.name
      return name ?? key
    },

    // 显示详细错误信息
    showDetailedError(message) {
      // 如果错误信息包含换行符，使用Modal显示详细信息
      if (message && message.includes('\n')) {
        this.$error({
          title: '导入失败',
          content: h => h('div', {
            style: {
              whiteSpace: 'pre-line',
              maxHeight: '400px',
              overflow: 'auto'
            }
          }, message),
          width: 600,
          okText: '确定'
        })
      } else {
        // 简单错误信息使用message显示
        this.$message.error(message)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.upload-section {
  .upload-actions {
    text-align: center;
  }
}

.import-summary {
  margin-bottom: 16px;
}

.import-actions {
  text-align: right;
}

/deep/ .ag-theme-balham {
  .ag-header-cell-text {
    font-weight: bold;
  }
}
</style>
