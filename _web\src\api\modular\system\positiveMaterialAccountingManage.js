/**
 * 正极材料核算管理
 *
 * <AUTHOR>
 * @date 2025年X月X日
 */
import { axios } from '@/utils/request'

/**
 * 正极材料核算分页列表
 *
 * <AUTHOR>
 */
export function getPositiveMaterialAccountingPage(parameter) {
  return axios({
    url: '/positiveMaterialAccounting/pageList',
    method: 'post',
    data: parameter
  })
}

/**
 * 正极材料核算列表
 *
 * <AUTHOR>
 */
export function getPositiveMaterialAccountingList(parameter) {
  return axios({
    url: '/positiveMaterialAccounting/list',
    method: 'post',
    data: parameter
  })
}

/**
 * 新增正极材料核算
 *
 * <AUTHOR>
 */
export function positiveMaterialAccountingAdd(parameter) {
  return axios({
    url: '/positiveMaterialAccounting/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑正极材料核算
 *
 * <AUTHOR>
 */
export function positiveMaterialAccountingEdit(parameter) {
  return axios({
    url: '/positiveMaterialAccounting/update',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除正极材料核算
 *
 * <AUTHOR>
 */
export function positiveMaterialAccountingDelete(parameter) {
  return axios({
    url: '/positiveMaterialAccounting/delete',
    method: 'post',
    data: parameter
  })
}

/**
 * 获取正极材料核算详情
 *
 * <AUTHOR>
 */
export function positiveMaterialAccountingGet(parameter) {
  return axios({
    url: '/positiveMaterialAccounting/get',
    method: 'post',
    data: parameter
  })
}

/**
 * 下载正极材料核算导入模板
 *
 * <AUTHOR>
 */
export function positiveMaterialAccountingDownloadTemplate() {
  return axios.post('/positiveMaterialAccounting/downloadTemplate', {}, {responseType: 'blob'})
}

/**
 * 导入正极材料核算Excel
 *
 * <AUTHOR>
 */
export function positiveMaterialAccountingImportExcel(file) {
  const formData = new FormData()
  formData.append('file', file)
  return axios({
    url: '/positiveMaterialAccounting/importExcel',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 检查正极材料核算重复的正极体系编号
 *
 * <AUTHOR>
 */
export function positiveMaterialAccountingCheckDuplicate(parameter) {
  return axios({
    url: '/positiveMaterialAccounting/checkDuplicate',
    method: 'post',
    data: parameter
  })
}

/**
 * 批量保存正极材料核算导入数据
 *
 * <AUTHOR>
 */
export function positiveMaterialAccountingBatchSaveImportData(parameter) {
  return axios({
    url: '/positiveMaterialAccounting/batchSaveImportData',
    method: 'post',
    data: parameter
  })
}