/**
 * 化学元素价格管理
 *
 * <AUTHOR>
 * @date 2025年7月1日
 */
import { axios } from '@/utils/request'

/**
 * 化学元素价格分页列表
 *
 * <AUTHOR>
 */
export function getChemicalElementPricePage(parameter) {
  return axios({
    url: '/chemicalElementPrice/pageList',
    method: 'post',
    data: parameter
  })
}

/**
 * 化学元素价格列表
 *
 * <AUTHOR>
 */
export function getChemicalElementPriceList(parameter) {
  return axios({
    url: '/chemicalElementPrice/list',
    method: 'post',
    data: parameter
  })
}

/**
 * 新增化学元素价格
 *
 * <AUTHOR>
 */
export function chemicalElementPriceAdd(parameter) {
  return axios({
    url: '/chemicalElementPrice/add',
    method: 'post',
    data: parameter
  })
}

/**
 * 更新化学元素价格
 *
 * <AUTHOR>
 */
export function chemicalElementPriceUpdate(parameter) {
  return axios({
    url: '/chemicalElementPrice/update',
    method: 'post',
    data: parameter
  })
}

/**
 * 批量保存化学元素价格
 *
 * <AUTHOR>
 */
export function chemicalElementPriceBatchSave(parameter) {
  return axios({
    url: '/chemicalElementPrice/batchSave',
    method: 'post',
    data: parameter
  })
}

/**
 * 编辑化学元素价格
 *
 * <AUTHOR>
 */
export function chemicalElementPriceEdit(parameter) {
  return axios({
    url: '/chemicalElementPrice/update',
    method: 'post',
    data: parameter
  })
}

/**
 * 删除化学元素价格
 *
 * <AUTHOR>
 */
export function chemicalElementPriceDelete(parameter) {
  return axios({
    url: '/chemicalElementPrice/delete',
    method: 'post',
    data: parameter
  })
}

/**
 * 获取化学元素价格详情
 *
 * <AUTHOR>
 */
export function chemicalElementPriceGet(parameter) {
  return axios({
    url: '/chemicalElementPrice/get',
    method: 'post',
    data: parameter
  })
}

/**
 * 检查化学元素价格重复的料号
 *
 * <AUTHOR>
 */
export function chemicalElementPriceCheckDuplicate(parameter) {
  return axios({
    url: '/chemicalElementPrice/checkDuplicate',
    method: 'post',
    data: parameter
  })
}

/**
 * 根据正极材料核算表数据获取化学元素价格数据
 *
 * <AUTHOR>
 */
export function getChemicalElementPriceFromAccounting(bomCostOverviewId, positiveElectrodeAccountingType) {
  return axios({
    url: '/chemicalElementPrice/getFromAccounting',
    method: 'get',
    params: {
      bomCostOverviewId: bomCostOverviewId,
      positiveElectrodeAccountingType: positiveElectrodeAccountingType
    }
  })
}
