package eve.sys.modular.bombill.materialprice.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.core.exception.ServiceException;
import eve.core.pojo.page.PageResult;
import eve.sys.modular.bombill.materialprice.dto.MaterialPriceExcelDto;
import eve.sys.modular.bombill.materialprice.entity.MaterialPrice;
import eve.sys.modular.bombill.materialprice.mapper.MaterialPriceMapper;
import eve.sys.modular.bombill.materialprice.param.MaterialPriceCheckDuplicateParam;
import eve.sys.modular.bombill.materialprice.service.IMaterialPriceService;
import eve.sys.modular.bombill.materialprice.util.MaterialPriceExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class MaterialPriceServiceImpl extends ServiceImpl<MaterialPriceMapper, MaterialPrice>
    implements IMaterialPriceService {

    @Override
    public PageResult<MaterialPrice> pageList(MaterialPrice param) {
        LambdaQueryWrapper<MaterialPrice> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StrUtil.isNotBlank(param.getPartNumber())) {
            queryWrapper.like(MaterialPrice::getPartNumber, param.getPartNumber());
        }
        
        if (StrUtil.isNotBlank(param.getDescription())) {
            queryWrapper.like(MaterialPrice::getDescription, param.getDescription());
        }
        
        if (null !=param.getMaterialTypeIds() && !param.getMaterialTypeIds().isEmpty()) {
            queryWrapper.in(MaterialPrice::getMaterialType, param.getMaterialTypeIds());
        }
        
        queryWrapper.orderByDesc(MaterialPrice::getCreateTime);
        Page<MaterialPrice> page = this.page(new Page<>(param.getPageNo(), param.getPageSize()), queryWrapper);
        return new PageResult<>(page);
    }
    
    @Override
    public List<MaterialPrice> list(MaterialPrice param) {
        LambdaQueryWrapper<MaterialPrice> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StrUtil.isNotBlank(param.getPartNumber())) {
            queryWrapper.like(MaterialPrice::getPartNumber, param.getPartNumber());
        }
        
        if (StrUtil.isNotBlank(param.getDescription())) {
            queryWrapper.like(MaterialPrice::getDescription, param.getDescription());
        }
        
        if (null !=param.getMaterialTypeIds() && !param.getMaterialTypeIds().isEmpty()) {
            queryWrapper.in(MaterialPrice::getMaterialType, param.getMaterialTypeIds());
        }
        
        queryWrapper.orderByDesc(MaterialPrice::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(MaterialPrice param) {
        if (StrUtil.isBlank(param.getPartNumber())) {
            throw new ServiceException(400, "物料代号不能为空");
        }

        // 检查是否已存在相同料号的记录
        LambdaQueryWrapper<MaterialPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialPrice::getPartNumber, param.getPartNumber());
        MaterialPrice existingMaterial = this.getOne(queryWrapper);

        if (existingMaterial != null) {
            // 存在相同料号，用新数据覆盖旧数据
            log.info("发现相同料号的材料价格记录，将进行覆盖更新: partNumber={}, oldId={}",
                param.getPartNumber(), existingMaterial.getId());

            param.setId(existingMaterial.getId());
            param.setCreateTime(existingMaterial.getCreateTime());
            param.setCreateUser(existingMaterial.getCreateUser());
            return this.updateById(param);
        } else {
            // 不存在相同料号，直接新增
            return this.save(param);
        }
    }

    @Override
    public Boolean delete(MaterialPrice param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        return this.removeById(param.getId());
    }

    @Override
    public Boolean update(MaterialPrice param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        return this.updateById(param);
    }

    @Override
    public MaterialPrice get(MaterialPrice param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        return this.getById(param.getId());
    }

    @Override
    public void exportExcel(MaterialPrice param, HttpServletResponse response) {
        try {
            String fileName = "材料价格一览表_" +
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";

            // 设置响应头
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" +
                URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20"));

            // 查询数据
            List<MaterialPrice> dataList = this.list(param);

            // 转换为Excel DTO
            List<MaterialPriceExcelDto> excelDataList = MaterialPriceExcelUtil.toExcelDtoList(dataList);

            // 写入Excel
            EasyExcel.write(response.getOutputStream(), MaterialPriceExcelDto.class)
                    .sheet("材料价格")
                    .doWrite(excelDataList);

            log.info("材料价格Excel导出成功，共导出 {} 条数据", excelDataList.size());

        } catch (Exception e) {
            log.error("材料价格Excel导出失败", e);
            e.printStackTrace();
        }
    }

    @Override
    public List<MaterialPriceExcelDto> importExcel(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new ServiceException(400, "上传文件不能为空");
        }

        try {
            List<MaterialPriceExcelDto> dataList = new ArrayList<>();

            // 使用同步读取方式，避免复杂的监听器实现
            List<MaterialPriceExcelDto> readData = EasyExcel.read(file.getInputStream())
                .head(MaterialPriceExcelDto.class)
                .sheet()
                .doReadSync();

            log.info("材料价格Excel读取成功，共读取 {} 条数据", readData.size());

            // 数据验证和处理
            for (MaterialPriceExcelDto dto : readData) {
                String errorMessage = MaterialPriceExcelUtil.validateExcelData(dto);
                dto.setErrorMessage(errorMessage);

                // 设置解析后的枚举值
                if (StrUtil.isBlank(errorMessage)) {
                    MaterialPrice entity = MaterialPriceExcelUtil.toEntity(dto);
                    dto.setMaterialType(entity.getMaterialType());
                    dto.setStructuralPart(entity.getStructuralPart());
                }

                dataList.add(dto);
            }

            return dataList;

        } catch (Exception e) {
            log.error("材料价格Excel导入失败", e);
            throw new ServiceException(500, "Excel导入失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSaveImportData(List<MaterialPriceExcelDto> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            throw new ServiceException(400, "导入数据不能为空");
        }

        // 过滤出没有错误的数据
        List<MaterialPriceExcelDto> validDataList = dataList.stream()
            .filter(dto -> StrUtil.isBlank(dto.getErrorMessage()))
            .collect(java.util.stream.Collectors.toList());

        if (validDataList.isEmpty()) {
            throw new ServiceException(400, "没有有效的数据可以导入");
        }

        try {
            // 转换为实体对象
            List<MaterialPrice> entityList = MaterialPriceExcelUtil.toEntityList(validDataList);

            // 处理料号唯一性：查询所有现有的料号
            List<String> partNumbers = entityList.stream()
                .map(MaterialPrice::getPartNumber)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(java.util.stream.Collectors.toList());

            // 查询数据库中已存在的料号记录
            LambdaQueryWrapper<MaterialPrice> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(MaterialPrice::getPartNumber, partNumbers);
            List<MaterialPrice> existingMaterials = this.list(queryWrapper);

            // 构建料号到ID的映射
            java.util.Map<String, MaterialPrice> existingMap = existingMaterials.stream()
                .collect(java.util.stream.Collectors.toMap(
                    MaterialPrice::getPartNumber,
                    material -> material,
                    (existing, replacement) -> existing // 如果有重复，保留第一个
                ));

            // 分类处理：新增和更新
            List<MaterialPrice> toInsert = new ArrayList<>();
            List<MaterialPrice> toUpdate = new ArrayList<>();
            int coverCount = 0; // 覆盖计数

            for (MaterialPrice entity : entityList) {
                if (StrUtil.isBlank(entity.getPartNumber())) {
                    continue; // 跳过料号为空的记录
                }

                MaterialPrice existing = existingMap.get(entity.getPartNumber());
                if (existing != null) {
                    // 存在相同料号，准备覆盖更新
                    entity.setId(existing.getId());
                    entity.setCreateTime(existing.getCreateTime());
                    entity.setCreateUser(existing.getCreateUser());
                    toUpdate.add(entity);
                    coverCount++;
                } else {
                    // 不存在相同料号，准备新增
                    toInsert.add(entity);
                }
            }

            boolean result = true;

            // 批量新增
            if (!toInsert.isEmpty()) {
                List<List<MaterialPrice>> insertPartitions = com.google.common.collect.Lists.partition(toInsert, 100);
                for (List<MaterialPrice> partition : insertPartitions) {
                    result = result && this.saveBatch(partition);
                }
            }

            // 批量更新
            if (!toUpdate.isEmpty()) {
                List<List<MaterialPrice>> updatePartitions = com.google.common.collect.Lists.partition(toUpdate, 100);
                for (List<MaterialPrice> partition : updatePartitions) {
                    result = result && this.updateBatchById(partition);
                }
            }

            log.info("材料价格批量导入成功，共处理 {} 条数据，其中新增 {} 条，覆盖更新 {} 条",
                entityList.size(), toInsert.size(), coverCount);

            return result;

        } catch (Exception e) {
            log.error("材料价格批量导入失败", e);
            throw new ServiceException(500, "批量导入失败：" + e.getMessage());
        }
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) {
        try {
            String fileName = "材料价格导入模板.xlsx";

            // 设置响应头，参考 TestFeeServiceImpl 的方式
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" +
                URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20"));

            // 写入空模板，参考 TestFeeServiceImpl 的方式
            EasyExcel.write(response.getOutputStream(), MaterialPriceExcelDto.class)
                    .sheet("模板")
                    .doWrite(new ArrayList<>());

            log.info("材料价格导入模板下载成功");

        } catch (Exception e) {
            log.error("材料价格导入模板下载失败", e);
            e.printStackTrace();
        }
    }

    @Override
    public List<String> checkDuplicatePartNumbers(MaterialPriceCheckDuplicateParam param) {
        if (param == null || param.getPartNumbers() == null || param.getPartNumbers().isEmpty()) {
            return new ArrayList<>();
        }

        log.info("开始检查重复料号，料号数量: {}, 排除ID: {}", param.getPartNumbers().size(), param.getExcludeId());

        // 构建查询条件
        LambdaQueryWrapper<MaterialPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MaterialPrice::getPartNumber, param.getPartNumbers());

        // 如果有排除ID，则排除该记录
        if (param.getExcludeId() != null) {
            queryWrapper.ne(MaterialPrice::getId, param.getExcludeId());
        }

        // 查询重复的记录
        List<MaterialPrice> duplicateRecords = this.list(queryWrapper);

        // 提取重复的料号
        List<String> duplicatePartNumbers = duplicateRecords.stream()
            .map(MaterialPrice::getPartNumber)
            .distinct()
            .collect(java.util.stream.Collectors.toList());

        log.info("检查重复料号完成，发现重复料号: {}", duplicatePartNumbers);
        return duplicatePartNumbers;
    }
}