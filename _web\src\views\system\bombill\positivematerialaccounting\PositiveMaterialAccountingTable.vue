<template>
	<div>
		<tableIndex ref="pbiTableIndex" :pageLevel='2' :tableTotal='tableTotal' :pageTitleShow=false :loading='loading'
			@paginationChange="handlePageChange" @paginationSizeChange="handlePageChange" @tableFocus="tableFocus"
			@tableBlur="tableBlur">
			<template #search>
				<pbiSearchContainer>
					<pbiSearchItem label='正极体系编号' :span="6">
						<a-input size='small' @keyup.enter.native='callFilter' v-model='queryParam.chemicalSystemCode'
							placeholder='请输入正极体系编号' />
					</pbiSearchItem>
					<pbiSearchItem label='正极体系' :span="6">
						<a-input size='small' @keyup.enter.native='callFilter' v-model='queryParam.chemicalSystem'
							placeholder='请输入正极体系' />
					</pbiSearchItem>

					<pbiSearchItem type='btn' :span="12">
						<div class="secondary-btn">
							<a-button class="mr10" @click="resetSearch">重置</a-button>
						</div>
						<div class="main-btn">
							<a-button type="primary" size="small" @click="callFilter" class="mr10">查询</a-button>
						</div>
						<!-- <div class="main-btn" v-if="hasPerm('positiveMaterialAccounting:add')"> -->
						<div class="main-btn">
							<a-button class="mr10" type="primary" size='small' @click="$refs.addPositiveMaterialAccounting.add()">
								新建
							</a-button>
						</div>

						<div class="main-btn">
							<a-button type="default" size='small' @click="handleImport">
								<a-icon type="upload" />
								导入
							</a-button>
						</div>
					</pbiSearchItem>
				</pbiSearchContainer>
			</template>

			<template #table>
				<ag-grid-vue :style='`height: ${tableHeight}px`' class='table ag-theme-balham' :tooltipShowDelay="0"
					:defaultColDef="defaultColDef" :grid-options="gridOptions" :columnDefs='columnDefs'
					:rowData='rowData' :suppressDragLeaveHidesColumns="true" :suppressMoveWhenColumnDragging="true" />
			</template>
		</tableIndex>

		<addPositiveMaterialAccounting ref="addPositiveMaterialAccounting" @ok="loadData" />
		<editPositiveMaterialAccounting ref="editPositiveMaterialAccounting" @ok="loadData" />
		<ImportPositiveMaterialAccountingModal ref="importModal" @ok="loadData" />
	</div>
</template>

<script>
import { getPositiveMaterialAccountingPage, positiveMaterialAccountingDelete } from "@/api/modular/system/positiveMaterialAccountingManage"
import { getChemicalElementList } from "@/api/modular/system/chemicalElementManage"
import Vue from 'vue'
import { DICT_TYPE_TREE_DATA } from '@/store/mutation-types'
export default {
	components: {
		addPositiveMaterialAccounting: () => import("./addPositiveMaterialAccounting"),
		editPositiveMaterialAccounting: () => import("./editPositiveMaterialAccounting"),
		ImportPositiveMaterialAccountingModal: () => import("./ImportPositiveMaterialAccountingModal"),
		actionRender: {
			template: `
        <div>
            <div class="btns">
                <a @click="params.onEdit(params.data)">编辑</a>
                <a-popconfirm placement="topRight" title="确认删除？" @confirm="() => params.onDel(params.data)">
                    <a>删除</a>
                </a-popconfirm>
            </div>
        </div>
        `
		},
	},
	data() {
		return {

			tableHeight: document.documentElement.clientHeight - 40 - 16 - 100,
			pageNo: 1,
			pageSize: 20,
			loading: false,
			tableTotal: 0,
			queryParam: { chemicalSystemCode: '', chemicalSystem: '' },
			rowData: [],
			defaultColDef: {
				filter: false,
				floatingFilter: false,
				editable: false
			},
			gridOptions: {},
			columnDefs: [
				{
					headerName: "序号",
					width: 70,
					field: "no",
					align: "center",
					pinned: 'left',
					cellRenderer: params => parseInt(params.node.id) + 1
				},
				{
					headerName: "正极体系编号",
					width: 120,
					field: "chemicalSystemCode",
					pinned: 'left',
					align: "center"

				},
				{
					headerName: "正极体系",
					width: 110,
					field: "chemicalSystem",
					pinned: 'left',
					align: "center"
				},
				{
					headerName: "加工费",
					width: 100,
					field: "processingFee",
					pinned: 'left',
					align: "center"
				},

			]
		}
	},

	methods: {
		resetSearch(){
			this.queryParam = { chemicalSystemCode: '', chemicalSystem: '' },
			this.callFilter()
		},
		getDictName(code,key) {
			const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
			let dict = dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
			let name = dict.find(item=>item.code == key)?.name
			return name ?? '-'
		},
		tableFocus() {
			this.$el.style.setProperty('--scroll-border-bottom-fixed', 'none');
			this.$el.style.setProperty('--scroll-display', 'unset');
			this.$el.style.setProperty('--scroll-border-bottom', '1px solid #Dee1e8');
		},
		tableBlur() {
			this.$el.style.setProperty('--scroll-border-bottom-fixed', '1px solid #dee1e8');
			this.$el.style.setProperty('--scroll-display', 'none');
			this.$el.style.setProperty('--scroll-border-bottom', 'none');
		},

		callFilter() {
			this.pageNo = 1
			this.$refs.pbiTableIndex.$refs.pbiPagination.handleWithoutChange(this.pageNo, this.pageSize)
			this.loadData()
		},

		handlePageChange(value) {
			this.pageNo = value.current
			this.pageSize = value.pageSize
			this.loadData()
		},
		getChemicalElementList() {
			this.loading = true
			getChemicalElementList({}).then(res => {
				if (res.success) {

					const groupByAccountingType = res.data.reduce((group, item) => {
						const { accountingType } = item;
						group[accountingType] = group[accountingType] ?? [];
						group[accountingType].push(item);
						return group;
					}, {});

					let columns = []

					for (const key in groupByAccountingType) {

						if (groupByAccountingType[key]) {

							let children = []

							for (const e of groupByAccountingType[key]) {

								children.push({
									headerName: e.elementName,
									minWidth: 100,
									flex: 1,
									field: e.accountingType + '_' + e.elementName,
									align: "center"
								})
							}

							columns.push({
								headerName: `每KG正极${this.getDictName('bom_bill_account_type',key)}用量(kg)`,
								children: children
							})

						}
					}

					columns.push({
						headerName: "操作",
						width: 120,
						field: "action",
						align: "center",
						pinned: 'right',
						cellRenderer: "actionRender",
						cellRendererParams: {
							onEdit: this.edit,
							onDel: this.del
						}
					})
					this.columnDefs = [...this.columnDefs, ...columns]
					this.loadData()
				}
			}).finally(() => {
				this.loading = false
			})
		},
		loadData() {
			this.loading = true
			getPositiveMaterialAccountingPage({
				pageNo: this.pageNo,
				pageSize: this.pageSize,
				...this.queryParam
			}).then(res => {
				if (res.success) {
					this.rowData = res.data.rows
					this.tableTotal = res.data.totalRows
				}
			}).finally(() => {
				this.loading = false
			})
		},

		edit(record) {
			this.$refs.editPositiveMaterialAccounting.edit(record)
		},

		del(record) {
			this.$confirm({
				title: '确认删除',
				content: '确定要删除该正极材料核算吗？',
				onOk: () => {
					this.loading = true
					positiveMaterialAccountingDelete({ id: record.id }).then(res => {
						if (res.success) {
							this.$message.success('删除成功')
							this.loadData()
						} else {
							this.$message.error('删除失败：' + res.message)
						}
					}).finally(() => {
						this.loading = false
					})
				}
			})
		},

		// 导入Excel
		handleImport() {
			this.$refs.importModal.show()
		}
	},

	created() {
		this.getChemicalElementList()
	}
}
</script>

<style lang="less" scoped="">
@import '/src/components/pageTool/style/pbiSearchItem.less';
:root {
	--scroll-display: none;
	--scroll-border-bottom: none;
	--scroll-border-bottom-fixed: '1px solid #dee1e8';
}
/deep/.searchItem .label{
    width: initial;
}
/deep/.ag-body-horizontal-scroll {
	border-bottom: var(--scroll-border-bottom) !important;
}

/deep/.ag-body-horizontal-scroll-viewport {
	display: var(--scroll-display) !important;
	border-bottom: var(--scroll-border-bottom) !important;
}

/deep/.ag-horizontal-left-spacer,
/deep/.ag-horizontal-right-spacer {
	border-bottom: var(--scroll-border-bottom-fixed) !important;
}

/deep/.ag-root {
	&.ag-layout-normal {
		padding: 0;
	}
}

/deep/.btns a {
	margin-right: 8px;
	color: #1890FF;
}

/deep/.element-link {
	color: #1890FF;
}

/deep/ .ant-pagination-options {
	margin-bottom: 0;
}

/deep/.ant-pagination-item-link {
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>