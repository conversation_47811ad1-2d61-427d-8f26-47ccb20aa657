package eve.sys.modular.bombill.chemicalelement.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.core.exception.ServiceException;
import eve.core.pojo.page.PageResult;
import eve.sys.modular.bombill.chemicalelement.entity.ChemicalElement;
import eve.sys.modular.bombill.chemicalelement.mapper.ChemicalElementMapper;
import eve.sys.modular.bombill.chemicalelement.service.IChemicalElementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChemicalElementServiceImpl extends ServiceImpl<ChemicalElementMapper, ChemicalElement> implements IChemicalElementService {

    @Override
    public PageResult<ChemicalElement> pageList(ChemicalElement param) {
        LambdaQueryWrapper<ChemicalElement> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StrUtil.isNotBlank(param.getElementName())) {
            queryWrapper.like(ChemicalElement::getElementName, param.getElementName());
        }
        
        if (StrUtil.isNotBlank(param.getAccountingType())) {
            queryWrapper.eq(ChemicalElement::getAccountingType, param.getAccountingType());
        }
        if (null != param.getAccountingTypeIds() && !param.getAccountingTypeIds().isEmpty()) {
            queryWrapper.and(
                x -> {
                    for (String item : param.getAccountingTypeIds()) {
                        x.or().like(ChemicalElement::getAccountingType, item);
                    }
                }
            );
        }
        queryWrapper.orderByAsc(ChemicalElement::getSort);
        Page<ChemicalElement> page = this.page(new Page<>(param.getPageNo(), param.getPageSize()), queryWrapper);
        return new PageResult<>(page);
    }

    @Override
    public List<ChemicalElement> list(ChemicalElement param) {
        LambdaQueryWrapper<ChemicalElement> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StrUtil.isNotBlank(param.getElementName())) {
            queryWrapper.like(ChemicalElement::getElementName, param.getElementName());
        }

        
        if (StrUtil.isNotBlank(param.getAccountingType())) {
            queryWrapper.eq(ChemicalElement::getAccountingType, param.getAccountingType());
        }
        if (null != param.getAccountingTypeIds() && !param.getAccountingTypeIds().isEmpty()) {
            queryWrapper.and(
                x -> {
                    for (String item : param.getAccountingTypeIds()) {
                        x.or().like(ChemicalElement::getAccountingType, item);
                    }
                }
            );
        }
        List<ChemicalElement> _chemicalElements = this.list(queryWrapper);

        List<ChemicalElement> chemicalElements = new ArrayList<>();

        for (ChemicalElement item : _chemicalElements) {

            String[] splits = item.getAccountingType().split(",");

            if (splits.length > 1) {

                Arrays.stream(splits).forEach(e -> {

                    ChemicalElement _item = JSONObject.parseObject(JSONObject.toJSONString(item),ChemicalElement.class);
                    _item.setAccountingType(e);

                    chemicalElements.add(_item);

                });

                continue;
            }
            chemicalElements.add(item);
        }
        
        return chemicalElements.stream().sorted(Comparator.comparing(ChemicalElement::getAccountingType).thenComparing(ChemicalElement::getSort)).collect(Collectors.toList());
    }

    @Override
    public Boolean add(ChemicalElement param) {
        param.setElementName(param.getElementName().trim());
        return this.save(param);
    }

    @Override
    public Boolean delete(ChemicalElement param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        return this.removeById(param.getId());
    }

    @Override
    public Boolean update(ChemicalElement param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        ChemicalElement updateEntity = ChemicalElement.builder()
            .id(param.getId())
            .elementName(param.getElementName().trim())
            .accountingType(param.getAccountingType())
            .unit(param.getUnit())
            .metalPremiseDisplay(param.getMetalPremiseDisplay())
            .sort(param.getSort())
            .build();
        return this.updateById(updateEntity);
    }

    @Override
    public ChemicalElement get(ChemicalElement param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        return this.getById(param.getId());
    }
}