<template>
  <div>
    <tableIndex
      ref="pbiTableIndex"
      :pageLevel='2'
      :tableTotal='tableTotal'
      :pageTitleShow=false 
      :loading='loading'
      @paginationChange="handlePageChange"
      @paginationSizeChange="handlePageChange"
      @tableFocus="tableFocus"
      @tableBlur="tableBlur" 
    >
      <template #search>
        <pbiSearchContainer>

          <pbiSearchItem label='客户类型' :span="6">
            <a-select
              size='small'
              @change='callFilter'
              v-model='queryParam.customerType'
              placeholder='请选择客户类型'
              allowClear
            >
              <a-select-option v-for="(item,i) in getDict('bom_bill_customer_type')" :value="item.code" :key="i">
                  {{ item.name }}
              </a-select-option>
            </a-select>
          </pbiSearchItem>

          <pbiSearchItem label='产品名称' :span="6">
            <a-input 
              size='small' 
              @keyup.enter.native='callFilter' 
              v-model='queryParam.productName'
              placeholder='请输入产品名称'
            />
          </pbiSearchItem>

          <pbiSearchItem label='产品状态'  :span="6" >
              <treeselect :limit='1' @input='callFilter' :max-height='200' style='width: 100%;' placeholder='请选择产品状态'
                  value-consists-of='BRANCH_PRIORITY' v-model='queryParam.productStatuses' :multiple='true'
                  :options='productStatuses' />
          </pbiSearchItem>

          <pbiSearchItem label='核算代码' :span="6" v-if='isShowAllSearch'>
            <a-input 
              size='small' 
              @keyup.enter.native='callFilter' 
              v-model='queryParam.accountingCode'
              placeholder='请输入核算代码'
            />
          </pbiSearchItem>

          <pbiSearchItem label='文件编号' :span="6" v-if='isShowAllSearch'>
            <a-input 
              size='small' 
              @keyup.enter.native='callFilter' 
              v-model='queryParam.bomFileNumber'
              placeholder='请输入BOM文件编号'
            />
          </pbiSearchItem>

          <pbiSearchItem label='正极体系'  :span="6" v-if='isShowAllSearch'>
              <treeselect :limit='1' @input='callFilter' :max-height='200' style='width: 100%;' placeholder='请选择正极体系'
                  value-consists-of='BRANCH_PRIORITY' v-model='queryParam.positiveElectrodeSystems' :multiple='true'
                  :options='positiveElectrodeSystems' />
          </pbiSearchItem>

          <pbiSearchItem label='研究院'  :span="6" v-if='isShowAllSearch'>
              <treeselect :limit='1' @input='callFilter' :max-height='200' style='width: 100%;' placeholder='请选择研究院'
                  value-consists-of='BRANCH_PRIORITY' v-model='queryParam.depts' :multiple='true'
                  :options='depts' />
          </pbiSearchItem>
          
          
          <pbiSearchItem type='btn' :span="isShowAllSearch ? 24 : 6">
            <div class="secondary-btn">
              <a-button class="mr10" @click="resetSearch">重置</a-button>
            </div>
            <div class="main-btn">
              <a-button type="primary" size="small" @click="callFilter" class="mr10">查询</a-button>
            </div>
            
            <div class="main-btn">
              <a-button type="primary" size='small' @click="$refs.bomCostOverviewApply.show(false)" class="mr10">
                新建申请
              </a-button>
            </div>

            <div class="main-btn">
              <a-tooltip title="选择要复制的行，点击复制按钮将创建新的BOM成本总览记录，包括相关的核算明细、场景和化学元素价格数据">
                <a-button class="mr10" type="default" size='small' @click="handleCopy" :loading="copyLoading" :disabled="selectedRows.length === 0">
                  <a-icon type="copy" />
                  复制 {{ selectedRows.length > 0 ? `(${selectedRows.length})` : '' }}
                </a-button>
              </a-tooltip>
            </div>

            <div class="main-btn">
              <a-button
                type="primary"
                size='small'
                class="mr10"
                @click="handleCompare"
                :disabled="selectedRows.length < 2"
              >
                对比 {{ selectedRows.length > 0 ? `(${selectedRows.length})` : '' }}
              </a-button>
            </div>

            <div class="main-btn" v-if="hasPerm('bomCostOverview:export')">
              <a-button type="default" size='small' @click="handleExport" :loading="exportLoading">
                <a-icon type="download" />
                导出
              </a-button>
            </div>

            <div class='toggle-btn'>
                <a-button size='small' type='link' @click='handleChangeSearch'>
                    {{isShowAllSearch ? '收起' : '展开'}}
                    <span v-if='isShowAllSearch'>
                        <a-icon type='double-left' />
                    </span>
                    <span v-else>
                        <a-icon type='double-right' />
                    </span>
                </a-button>
            </div>
          </pbiSearchItem>
        </pbiSearchContainer>
      </template>
      
      <template #table>
        <!-- 选中行统计信息 -->
        <div v-if="selectedRows.length > 0" style="margin-bottom: 10px; padding: 8px; background-color: #f0f9ff; border: 1px solid #bae7ff; border-radius: 4px;">
          <span style="color: #1890ff; font-weight: bold;">
            已选中 {{ selectedRows.length }} 行
          </span>
          <span style="margin-left: 15px; color: #666;">
            涉及场景: {{ selectedScenariosText }}
          </span>
          <span style="margin-left: 15px; color: #666;">
            涉及核算代码: {{ selectedAccountingCodesText }}
          </span>
        </div>

        <ag-grid-vue
          :style='`height: ${tableHeight}px`'
          class='table ag-theme-balham'
          :tooltipShowDelay="0"
          :defaultColDef="defaultColDef"
          :grid-options="gridOptions"
          :columnDefs='columnDefs'
          :rowData='rowData'
          :suppressDragLeaveHidesColumns="true"
          :suppressMoveWhenColumnDragging="true"
          @grid-ready="onGridReady"
        />
      </template>
    </tableIndex>
    
    <addBomCostOverview ref="addBomCostOverview" @ok="loadData" />
    <editBomCostOverview ref="editBomCostOverview" @ok="loadData" />
    <BomCostOverviewApply ref="bomCostOverviewApply" @show="handleModeSwitch" @success="loadData" />

    <!-- BOM成本对比弹窗 -->
    <BomCostCompareModal
      :visible.sync="compareModalVisible"
      :selectedScenarios="selectedScenariosForCompare"
    />
  </div>
</template>

<script>
import { getBomCostOverviewScenarioGroupPageList, bomCostOverviewDelete, bomCostOverviewExport, bomCostOverviewCopy } from "@/api/modular/system/bomCostOverviewManage"
import { getPositiveMaterialAccountingList } from "@/api/modular/system/positiveMaterialAccountingManage"
import { getChemicalElementPriceList, chemicalElementPriceAdd } from "@/api/modular/system/chemicalElementPriceManage"
import { getChemicalElementList } from "@/api/modular/system/chemicalElementManage"
import Vue from 'vue'
import { DICT_TYPE_TREE_DATA } from '@/store/mutation-types'
import BomCostOverviewApply from './BomCostOverviewApply.vue'
import BomCostCompareModal from './BomCostCompareModal.vue'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
export default {
  components:{
    addBomCostOverview: () => import("./addBomCostOverview"),
    editBomCostOverview: () => import("./editBomCostOverview"),
    BomCostOverviewApply,
    BomCostCompareModal,
    statusRender:{
       template:`
          <div>{{params.onShow(params)}}</div>
       `
    },
    Treeselect,
    auditStatusRender:{
      template: `
        <div>
          <a v-if="params.value == 0" @click="params.openJIRA(params.data.issueKey)">{{params.getAuditStatusTxt()[params.value]}}</a>
          <span v-else>{{params.getAuditStatusTxt()[params.value]}}</span>
        </div>
      `
    },
    actionRender:{
        template: `
        <div>
            <div class="btns">
                <a @click="params.onEdit(params.data)">{{params.data.auditStatus == 1 ? '核算' : '查看明细'}}</a>
            </div>
        </div>
        `
    },
  },
  data() {
    return {
      isShowAllSearch: false,
      tableHeight: document.documentElement.clientHeight - 40 - 16 - 100,
      templateHeight: document.documentElement.clientHeight - 40 - 16 - 100,
      pageNo: 1,
      pageSize: 20,
      loading: false,
      exportLoading: false,
      copyLoading: false,
      tableTotal: 0,
      // 对比弹窗相关
      compareModalVisible: false,
      selectedScenariosForCompare: [],
      depts: [],
      positiveElectrodeSystems: [],
      productStatuses: [],
      queryParam: {
        positiveElectrodeSystems: [],
        depts: [],
        productStatuses: [],
        accountingCode: '',
        productName: '',
        bomFileNumber: '',
        customerType: undefined,
      },
      rowData: [],
      selectedRows: [], // 选中的行数据
      gridApi: null, // 表格API
      columnApi: null, // 列API
      elementPriceCache: {}, // 化学元素价格缓存
      requestingKeys: new Set(), // 正在请求的缓存键，防止重复请求
      refreshingCells: false, // 标记是否正在刷新单元格，避免循环
      defaultColDef: {
        filter: false,
        floatingFilter: false,
        editable: false,
      },
      gridOptions: {
        rowSelection: 'multiple',
        onSelectionChanged: this.onSelectionChanged
      },
      baseColumnDefs: [
        {
          width: 60,
          align: "center",
          checkboxSelection: true,
          headerCheckboxSelection: true,
          headerCheckboxSelectionFilteredOnly: true,
          pinned: 'left'
        },
        {
          headerName: "序号",
          width: 70,
          field: "no",
          align: "center",
          pinned: 'left',
          cellRenderer: params => parseInt(params.node.id) + 1
        },
        {
          headerName: "核算代码",
          width: 100,
          field: "accountingCode",
          pinned: 'left',
          align: "center"
        },
        {
          headerName: "场景",
          pinned: 'left',
          width: 80,
          field: "scenarioId",
          align: "center",
          cellRenderer: params => {
            const scenarioText = '场景' + params.value || '-'
            return `<span>${scenarioText}</span>`
          },
          // 添加悬浮提示功能
          tooltipValueGetter: params => {
            return this.getScenarioElementPriceTooltip(params.data)
          },
          tooltipComponentParams: {
            color: '#ffffcc',
            type: 'scenarioElementPrice'
          }
        },
        
        {
          headerName: "核算日期",
          width: 100,
          field: "accountingDate",
          align: "center",
          cellRenderer: params => {
            if (params.value) {
              return new Date(params.value).toLocaleDateString('zh-CN')
            }
            return '-'
          }
        },
        {
          headerName: "客户类型",
          width: 90,
          field: "customerType",
          align: "center",
          cellRenderer: "statusRender",
          cellRendererParams:{
             onShow: this.customerTypeShow,
          }
        },
        {
          headerName: "产品名称",
          width: 120,
          field: "productName",
          align: "center"
        },
        {
          headerName: "产品状态",
          width: 90,
          field: "productStatus",
          align: "center",
          cellRenderer: "statusRender",
          cellRendererParams:{
             onShow: this.productStatusShow,
          }
        },
        
        {
          headerName: "正极体系",
          width: 150,
          field: "chemicalSystemCode",
          align: "center",
          cellRenderer: params => {
            if (params.value) {
              // 如果包含分号，说明是聚合的多个正极体系编号
              if (params.value.includes(';')) {
                const codes = params.value.split(';')
                return codes.length > 3
                  ? `${codes.slice(0, 3).join(';')}...`
                  : params.value
              }
              return params.value
            }
            return '-'
          },
          tooltipField: "chemicalSystemCode" // 悬浮显示完整内容
        },
        {
          headerName: "容量(Ah)",
          width: 80,
          field: "ratedCapacity",
          align: "center",
          cellRenderer: params => params.value ? `${params.value}Ah` : '-'
        },
        {
          headerName: "能量(Wh)",
          width: 80,
          field: "ratedEnergy",
          align: "center",
          cellRenderer: params => params.value ? `${params.value}Wh` : '-'
        },
        {
          headerName: "文件编号",
          minWidth: 170,
          field: "bomFileNumber",
          align: "center",
          flex:1
        },
        {
          headerName: "需求人",
          width: 120,
          field: "requesterName",
          align: "center"
        },
        {
          headerName: "用途说明",
          width: 150,
          field: "usageDescription",
        },
        {
          headerName: "状态",
          width: 100,
          field: "auditStatus",
          align: "center",
          //pinned: 'right',
          
          cellRenderer: 'auditStatusRender',/* params => {
            const statusMap = { 0: '审核中', 1: '核算中', 2: '已完成', 3: '驳回' }
            return statusMap[params.value] || '-'
          }, */
          cellRendererParams:{
            openJIRA: this.openJIRA,
            getAuditStatusTxt: this.getAuditStatusTxt
          }
        },
        {
          headerName: "详情",
          width: 120,
          field: "action",
          align: "center",
          pinned: 'right',
          cellRenderer: "actionRender",
          cellRendererParams: {
            onEdit: this.toBombillDetail,
            onDel: this.del
          }
        }
      ]
    }
  },

  computed: {
    columnDefs() {
      const columns = [...this.baseColumnDefs]

      // 找到"能量(Wh)"列的位置
      const energyIndex = columns.findIndex(col => col.headerName === "能量(Wh)")

      // 如果有权限，在"能量(Wh)"列后面插入"BOM成本(元/Wh)"列
      if (energyIndex !== -1 && this.hasPerm('bomCostOverview:pageList')) {
        const bomCostColumn = {
          headerName: "BOM成本(元/Wh)",
          children: [
            {
              headerName: "化学材料",
              width: 100,
              field: "chemicalElementPriceTotal",
              align: "center",
              cellRenderer: params => {
                if (params.value !== null && params.value !== undefined) {
                  return `¥${Number(params.value).toFixed(3)}`
                }
                return '-'
              },
            },
            {
              headerName: "结构件",
              width: 100,
              field: "structurePriceTotal",
              align: "center",
              cellRenderer: params => {
                if (params.value !== null && params.value !== undefined) {
                  return `¥${Number(params.value).toFixed(3)}`
                }
                return '-'
              },
            },
            {
              headerName: "合计",
              width: 100,
              field: "priceTotal",
              align: "center",
              cellRenderer: params => {
                if (params.value !== null && params.value !== undefined) {
                  return `¥${Number(params.value).toFixed(3)}`
                }
                return '-'
              },
            },
          ]
        }

        columns.splice(energyIndex + 1, 0, bomCostColumn)
      }

      return columns
    },
    // 选中行涉及的场景文本
    selectedScenariosText() {
      const scenarios = [...new Set(this.selectedRows.map(row => row.scenarioId).filter(id => id !== null && id !== undefined))]
      return scenarios.length > 3
        ? `${scenarios.slice(0, 3).join(', ')}等${scenarios.length}个场景`
        : scenarios.join(', ') || '无'
    },

    // 选中行涉及的核算代码文本
    selectedAccountingCodesText() {
      const codes = [...new Set(this.selectedRows.map(row => row.accountingCode).filter(code => code))]
      return codes.length > 3
        ? `${codes.slice(0, 3).join(', ')}等${codes.length}个代码`
        : codes.join(', ') || '无'
    }
  },

  methods: {
    getAuditStatusTxt(){
      return { 0: '审核中', 1: '核算中', 2: '已完成', 3: '驳回' }
    },
    openJIRA(issueKey){
      let $url = `http://jira.evebattery.com/browse/`+ issueKey+`?auth=` + Vue.ls.get("jtoken");
      window.open($url, "_blank");
    },
    // 处理对比功能
    handleCompare() {
      if (this.selectedRows.length < 2) {
        this.$message.warning('请至少选择2个场景进行对比')
        return
      }

      console.log('选中的行数据:', this.selectedRows)

      // 准备对比数据
      this.selectedScenariosForCompare = this.selectedRows.map(row => ({
        id: row.scenarioId,
        uid: row.bomCostOverviewId + '' + row.scenarioId,
        productName: row.productName,
        scenarioName: row.scenarioName || `场景${row.scenarioId}`,
        bomCostOverviewId: row.bomCostOverviewId
      }))

      console.log('准备对比的场景数据:', this.selectedScenariosForCompare)

      // 显示对比弹窗
      this.compareModalVisible = true

      this.$message.success(`已选择${this.selectedRows.length}个场景进行对比`)
    },

    // 导出Excel
    handleExport() {
      this.exportLoading = true
      bomCostOverviewExport({
        ...this.queryParam,
        pageNo: this.pageNo,
        pageSize: this.pageSize
      }).then(res => {
        // 生成文件名
        const now = new Date()
        const timestamp = now.getFullYear() +
          String(now.getMonth() + 1).padStart(2, '0') +
          String(now.getDate()).padStart(2, '0') + '_' +
          String(now.getHours()).padStart(2, '0') +
          String(now.getMinutes()).padStart(2, '0') +
          String(now.getSeconds()).padStart(2, '0')

        const fileName = `BOM成本总览表_${timestamp}.xlsx`
        const _res = res.data
        let blob = new Blob([_res])
        let downloadElement = document.createElement("a")
        //创建下载的链接
        let href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        //下载后文件名
        downloadElement.download = fileName
        document.body.appendChild(downloadElement)
        //点击下载
        downloadElement.click()
        //下载完成移除元素
        document.body.removeChild(downloadElement)
        //释放掉blob对象
        window.URL.revokeObjectURL(href)
        this.exportLoading = false
      }).catch(error => {
        console.error('导出失败:', error)
        this.$message.error('导出失败，请重试')
        this.exportLoading = false
      })
    },

    // 复制选中数据
    handleCopy() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要复制的数据')
        return
      }

      // 检查选中数据的有效性
      const invalidRows = this.selectedRows.filter(row => !row.bomCostOverviewId || !row.scenarioId)
      if (invalidRows.length > 0) {
        this.$message.error('选中的数据中存在无效记录，请重新选择')
        return
      }

      // 显示选中数据的详细信息
      const accountingCodes = [...new Set(this.selectedRows.map(row => row.accountingCode))].join(', ')
      const scenarioIds = [...new Set(this.selectedRows.map(row => row.scenarioId))].join(', ')

      this.$confirm({
        title: '确认复制',
        content: `确定要复制选中的 ${this.selectedRows.length} 条数据吗？

涉及核算代码: ${accountingCodes}
涉及场景ID: ${scenarioIds}

复制后将生成新的BOM成本总览记录，包括：
• BOM成本总览数据
• BOM核算明细数据
• 场景数据
• 化学元素价格数据`,
        onOk: () => {
          this.copyLoading = true

          // 构造复制参数
          const copyParam = {
            selectedRows: this.selectedRows.map(row => ({
              bomCostOverviewId: row.bomCostOverviewId,
              scenarioMainId: row.id, // 场景主键ID
              scenarioId: row.scenarioId,
              accountingCode: row.accountingCode
            }))
          }

          console.log('开始复制数据，参数:', copyParam)

          bomCostOverviewCopy(copyParam).then(res => {
            if (res.success) {
              this.$message.success(`复制成功！已复制 ${this.selectedRows.length} 条数据`)
              this.loadData() // 重新加载数据

              // 清空选中状态
              if (this.gridApi) {
                this.gridApi.deselectAll()
              }
            } else {
              this.$message.error('复制失败：' + (res.message || '未知错误'))
            }
          }).catch(error => {
            console.error('复制失败:', error)
            let errorMessage = '复制失败，请重试'
            if (error.response && error.response.data && error.response.data.message) {
              errorMessage = '复制失败：' + error.response.data.message
            }
            this.$message.error(errorMessage)
          }).finally(() => {
            this.copyLoading = false
          })
        }
      })
    },

    // 处理模式切换 - 子组件内部已经切换了模式，这里不需要做任何操作
    handleModeSwitch() {
      // 模式切换已在子组件内部完成，这里可以添加其他需要的逻辑
    },
    getDict(code) {
			const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
			return dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
		},
    getDictName(code,key) {
      const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
      let dict = dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
          let name = dict.find(item=>item.code == key)?.name
          return name ?? '-'
    },
    customerTypeShow(record){
      
      return this.getDictName('bom_bill_customer_type', record.value+'' )
    },
    productStatusShow(record){
      return this.getDictName('product_state_status', record.value+'' )
    },

    // 获取场景化学元素价格提示文本
    getScenarioElementPriceTooltip(rowData) {
      if (!rowData || !rowData.scenarioId) {
        return '暂无场景信息'
      }

      const scenarioId = rowData.scenarioId
      const bomCostOverviewId = rowData.bomCostOverviewId
      const accountingType = rowData.accountingType || rowData.positiveElectrodeAccountingType

      console.log('获取场景提示信息:', { scenarioId, bomCostOverviewId, accountingType })

      if (!bomCostOverviewId) {
        return `场景${scenarioId}：暂无BOM成本总览信息`
      }

      if (!accountingType) {
        return `场景${scenarioId}：暂无正极核算类型信息`
      }

      // 尝试从缓存中获取化学元素价格数据
      const cacheKey = `${bomCostOverviewId}_${scenarioId}_${accountingType}`
      console.log('查找缓存键:', cacheKey)

      if (this.elementPriceCache && this.elementPriceCache[cacheKey]) {
        console.log('从缓存获取数据:', this.elementPriceCache[cacheKey])
        return this.formatElementPriceTooltip(scenarioId, accountingType, this.elementPriceCache[cacheKey])
      }

      // 检查是否正在请求中，避免重复请求
      if (this.requestingKeys.has(cacheKey)) {
        console.log('数据正在请求中，返回加载状态...')
        return `场景${scenarioId}化学元素价格：\n正在加载...`
      }

      // 如果正在刷新单元格，不发起新的请求
      if (this.refreshingCells) {
        console.log('正在刷新单元格，返回加载状态...')
        return `场景${scenarioId}化学元素价格：\n正在加载...`
      }

      // 如果缓存中没有且没有正在请求，异步获取数据
      console.log('缓存中没有数据，开始异步获取...')
      this.fetchElementPricesForTooltip(bomCostOverviewId, scenarioId, accountingType)

      return `场景${scenarioId}化学元素价格：\n正在加载...`
    },

    // 格式化化学元素价格提示文本
    formatElementPriceTooltip(scenarioId, accountingType, elementPrices) {
      if (!elementPrices || elementPrices.length === 0) {
        return `场景${scenarioId}：暂无化学元素价格数据`
      }

      // 根据核算类型确定价格类型描述
      const priceTypeDesc = this.getAccountingTypeDesc(accountingType)
      let tooltipText = `场景${scenarioId}化学元素价格（${priceTypeDesc}）：\n`

      elementPrices.forEach(element => {
        const elementName = element.elementName || '未知元素'
        const price = element.cnyPrice ? Number(element.cnyPrice).toFixed(3) : '未设置'
        const unit = element.unit || '单位未知'
        // 格式：元素名称-相对应的核算类型的金属价或盐价-单位
        tooltipText += `${elementName}-${price}-${unit}\n`
      })

      return tooltipText.trim()
    },

    // 获取核算类型描述
    getAccountingTypeDesc(accountingType) {
      switch (accountingType) {
        case 1:
          return '金属价'
        case 2:
          return '盐价'
        default:
          return '未知类型'
      }
    },

    // 异步获取化学元素价格（用于悬浮提示）
    async fetchElementPricesForTooltip(bomCostOverviewId, scenarioId, accountingType) {
      const cacheKey = `${bomCostOverviewId}_${scenarioId}_${accountingType}`

      // 标记正在请求
      this.requestingKeys.add(cacheKey)

      try {
        console.log(`开始获取化学元素价格数据:`, {
          bomCostOverviewId,
          scenarioId,
          accountingType
        })

        // 调用化学元素价格API获取数据
        const response = await getChemicalElementPriceList({
          bomCostOverviewId: bomCostOverviewId,
          positiveElectrodeAccountingType: accountingType
        })

        console.log('化学元素价格API响应:', response)

        if (response.success && response.data) {
          console.log('API返回的原始数据:', response.data)

          // 过滤出指定场景的化学元素价格数据
          const elementPrices = response.data.filter(item => {
            const match = item.scenarioId === scenarioId //&& item.cnyPrice > 0
            console.log(`数据项过滤:`, {
              item: item,
              scenarioIdMatch: item.scenarioId === scenarioId,
              priceValid: item.cnyPrice > 0,
              finalMatch: match
            })
            return match
          })

          console.log(`场景${scenarioId}过滤后的化学元素价格数据:`, elementPrices)

          // 缓存数据
          const cacheKey = `${bomCostOverviewId}_${scenarioId}_${accountingType}`
          if (!this.elementPriceCache) {
            this.elementPriceCache = {}
          }
          this.elementPriceCache[cacheKey] = elementPrices

          console.log(`数据已缓存到键: ${cacheKey}`, elementPrices)

          // 数据已缓存，需要刷新tooltip以显示最新数据
          console.log('数据加载完成并已缓存，刷新tooltip以显示最新数据')

          // 延迟刷新，确保数据完全缓存后再刷新
          setTimeout(() => {
            if (this.gridApi && !this.refreshingCells) {
              this.refreshingCells = true
              console.log('开始刷新场景列单元格...')

              try {
                // 只刷新场景列，避免全表刷新
                this.gridApi.refreshCells({
                  columns: ['scenarioId'],
                  force: true
                })
                console.log('已刷新场景列单元格，tooltip应该显示最新数据')
              } finally {
                // 延迟重置刷新标记，确保刷新完成
                setTimeout(() => {
                  this.refreshingCells = false
                  console.log('刷新标记已重置')
                }, 300)
              }
            }
          }, 200)
        } else {
          console.warn('API调用成功但没有返回有效数据:', response)
        }

      } catch (error) {
        console.error('获取化学元素价格失败:', error)

        // 缓存错误状态，避免重复请求
        if (!this.elementPriceCache) {
          this.elementPriceCache = {}
        }
        this.elementPriceCache[cacheKey] = []
      } finally {
        // 清理请求状态
        this.requestingKeys.delete(cacheKey)
        console.log(`请求状态已清理: ${cacheKey}`)
      }
    },

    // 清理化学元素价格缓存
    clearElementPriceCache() {
      this.elementPriceCache = {}
      this.requestingKeys.clear()
      this.refreshingCells = false
      console.log('化学元素价格缓存、请求状态和刷新标记已清理')
    },

    // 测试创建化学元素价格数据
    async testCreateChemicalElementPriceData(rowData) {
      try {
        console.log('=== 开始测试创建化学元素价格数据 ===')
        console.log('场景数据:', rowData)

        // 模拟场景对象
        const scenarioData = {
          scenarioId: rowData.scenarioId,
          bomCostOverviewId: rowData.bomCostOverviewId
        }

        console.log('准备创建化学元素价格数据，场景信息:', scenarioData)

        // 这里可以调用后端的insertBatch方法
        // 由于没有直接的API，我们先检查是否有化学元素数据

        // 先查询化学元素列表
        const elementResponse = await this.getChemicalElementList()
        console.log('化学元素列表:', elementResponse)

        if (elementResponse && elementResponse.success && elementResponse.data) {
          console.log('找到化学元素数据，数量:', elementResponse.data.length)

          // 为每个化学元素创建价格记录
          const pricePromises = elementResponse.data.map(element => {
            const priceData = {
              scenarioId: scenarioData.scenarioId,
              bomCostOverviewId: scenarioData.bomCostOverviewId,
              chemicalElementId: element.id,
              positiveElectrodeAccountingType: parseInt(element.accountingType) || 2,
              cnyPrice: Math.random() * 100 + 50, // 随机价格用于测试
              //usdPrice: (Math.random() * 100 + 50) / 7.2,
              unit: element.unit || '单位未知'
            }

            console.log('创建化学元素价格记录:', priceData)
            return this.createChemicalElementPrice(priceData)
          })

          const results = await Promise.all(pricePromises)
          console.log('批量创建结果:', results)
        }

        console.log('=== 化学元素价格数据创建测试完成 ===')
      } catch (error) {
        console.error('测试创建化学元素价格数据失败:', error)
      }
    },

    // 获取化学元素列表
    async getChemicalElementList() {
      try {
        const response = await getChemicalElementList({})
        console.log('化学元素API响应:', response)
        return response
      } catch (error) {
        console.error('获取化学元素列表失败:', error)
        return null
      }
    },

    // 创建化学元素价格记录
    async createChemicalElementPrice(priceData) {
      try {
        const response = await chemicalElementPriceAdd(priceData)
        console.log('创建化学元素价格API响应:', response)
        return response
      } catch (error) {
        console.error('创建化学元素价格记录失败:', error)
        return { success: false, error: error }
      }
    },

    // 行选择变化处理
    onSelectionChanged() {
      if (this.gridApi) {
        this.selectedRows = this.gridApi.getSelectedRows()
        console.log('选中的行数据:', this.selectedRows)
        console.log('选中行数:', this.selectedRows.length)

        // 显示选中行的统计信息
        if (this.selectedRows.length > 0) {
          const scenarios = [...new Set(this.selectedRows.map(row => row.scenarioId))].join(', ')
          const accountingCodes = [...new Set(this.selectedRows.map(row => row.accountingCode))].join(', ')
          console.log(`选中行统计: 涉及场景=[${scenarios}], 涉及核算代码=[${accountingCodes}]`)
        }
      }
    },

    // 获取选中的行数据（供外部调用）
    getSelectedRows() {
      return this.selectedRows
    },

    // 表格准备就绪
    onGridReady(params) {
      this.gridApi = params.api
      this.columnApi = params.columnApi
      console.log('BOM成本总览表格初始化完成')
    },

    getPositiveMaterialAccountingList(){
      getPositiveMaterialAccountingList({}).then(res => {
        let _datas = []
        for (const item of res.data) {
            let _item = {
                id: item.id, // 使用主键ID
                label: item.chemicalSystemCode, // 显示正极体系编号
                chemicalSystem: item.chemicalSystem, // 正极体系名称
                chemicalSystemCode: item.chemicalSystemCode // 保留编号用于显示
            }
            _datas.push(_item)
        }
        this.positiveElectrodeSystems = _datas
      })
    },
    getDict(code) {
			const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
			return dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
		},
    getProductStatuses(){
      let items = this.getDict('product_state_status')
        items.sort((a,b)=>a.sort-b.sort)
        let _datas = []
        for (const item of items) {
            let _item = {
                id: parseInt(item.code),
                label: item.name
            }
            _datas.push(_item)
        }
        this.productStatuses = _datas
    },
    getDepts(){
        let items = this.getDict('bom_bill_depts')
        items.sort((a,b)=>a.sort-b.sort)
        let _depts = []
        for (const item of items) {
            let _item = {
                id: item.code,
                label: item.name
            }
            _depts.push(_item)
        }
        this.depts = _depts
    },
    handleChangeSearch() {
          this.isShowAllSearch = !this.isShowAllSearch;
          this.tableHeight = this.isShowAllSearch ? this.templateHeight - 80 : this.templateHeight
    },
    tableFocus() {
        this.$el.style.setProperty('--scroll-border-bottom-fixed', 'none');
        this.$el.style.setProperty('--scroll-display', 'unset');
        this.$el.style.setProperty('--scroll-border-bottom', '1px solid #Dee1e8');
    },
    // 鼠标移出
    tableBlur() {
        this.$el.style.setProperty('--scroll-border-bottom-fixed', '1px solid #dee1e8');
        this.$el.style.setProperty('--scroll-display', 'none');
        this.$el.style.setProperty('--scroll-border-bottom', 'none');
    },

    resetSearch(){
      this.queryParam = {
        positiveElectrodeSystems: [],
        depts: [],
        productStatuses: [],
        accountingCode: '',
        productName: '',
        bomFileNumber: '',
        customerType: undefined,
      },
      this.callFilter()
    },

    callFilter() {
      this.pageNo = 1
      this.$refs.pbiTableIndex.$refs.pbiPagination.handleWithoutChange(this.pageNo,this.pageSize)
      this.loadData()
    },
    
    handlePageChange(value) {
      this.pageNo = value.current
      this.pageSize = value.pageSize
      this.loadData()
    },
    
    loadData() {
      this.loading = true
      // 清理化学元素价格缓存，确保数据实时性
      this.clearElementPriceCache()
      console.log('调用场景分组分页查询，查询参数:', {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        ...this.queryParam
      })

      getBomCostOverviewScenarioGroupPageList({
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        ...this.queryParam
      }).then(res => {
        if (res.success) {
          console.log('SQL层场景分组查询成功，返回数据:', res.data)
          this.rowData = res.data.rows
          this.tableTotal = res.data.totalRows

          // 显示前几条数据的结构，便于调试
          if (res.data.rows && res.data.rows.length > 0) {
            console.log('第一条场景数据结构:', res.data.rows[0])
            console.log('数据字段检查:')
            console.log('- id (场景ID):', res.data.rows[0].id)
            console.log('- scenarioId:', res.data.rows[0].scenarioId)
            console.log('- bomCostOverviewId:', res.data.rows[0].bomCostOverviewId)
            console.log('- chemicalSystemCode:', res.data.rows[0].chemicalSystemCode)
            console.log('- accountingCode:', res.data.rows[0].accountingCode)
            console.log('- customerType:', res.data.rows[0].customerType)
            console.log('- productStatus:', res.data.rows[0].productStatus)
            console.log('- auditStatus:', res.data.rows[0].auditStatus)
            console.log('- accountingType:', res.data.rows[0].accountingType)

            // 显示分组信息（按bomCostOverviewId+scenarioId分组）
            const scenarioGroups = {}
            res.data.rows.forEach(row => {
              const groupKey = `${row.bomCostOverviewId}_${row.scenarioId}`
              if (!scenarioGroups[groupKey]) {
                scenarioGroups[groupKey] = {
                  count: 0,
                  bomCostOverviewId: row.bomCostOverviewId,
                  scenarioId: row.scenarioId,
                  chemicalSystems: row.chemicalSystemCode, // 保持不变，后端会填充这个字段
                  accountingCode: row.accountingCode
                }
              }
              scenarioGroups[groupKey].count++
            })
            console.log('SQL层场景分组信息（按bomCostOverviewId+scenarioId）:', scenarioGroups)
            console.log('每个分组包含的正极体系编号已聚合显示')

            // 测试化学元素价格API调用（使用第一条数据）
            if (res.data.rows.length > 0) {
              const firstRow = res.data.rows[0]
              if (firstRow.bomCostOverviewId && firstRow.accountingType) {
                console.log('开始测试化学元素价格API...')
                //this.testChemicalElementPriceAPI(firstRow.bomCostOverviewId, firstRow.accountingType)

                /* // 同时测试创建化学元素价格数据
                console.log('测试创建化学元素价格数据...')
                this.testCreateChemicalElementPriceData(firstRow)

                // 添加测试数据到缓存
                console.log('添加测试数据到缓存...')
                this.addTestDataToCache(firstRow) */
              }
            }
          }
        }
      }).finally(() => {
        this.loading = false
      })
    },
    
    toBombillDetail(record) {
      console.log('查看明细，记录数据:', record)

      // 现在返回的是场景数据，需要使用bomCostOverviewId作为详情页面的参数
      const bomCostOverviewId = record.bomCostOverviewId || record.id

      this.$router.push({
        path: '/bombill/account',
        query: {
          id: bomCostOverviewId
        }
      })
    },
    
    del(record) {
      console.log('删除记录，记录数据:', record)

      // 删除的是BOM成本总览记录，需要使用bomCostOverviewId
      const bomCostOverviewId = record.bomCostOverviewId || record.id

      this.$confirm({
        title: '确认删除',
        content: '确定要删除该BOM成本总览记录吗？',
        onOk: () => {
          this.loading = true
          bomCostOverviewDelete({ id: bomCostOverviewId }).then(res => {
            if (res.success) {
              this.$message.success('删除成功')
              this.loadData()
            } else {
              this.$message.error('删除失败：' + res.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  },
  
  created() {
    this.getProductStatuses()
    this.getPositiveMaterialAccountingList()
    this.getDepts()
    this.loadData()
  }
}
</script>

<style lang="less" scoped="">
@import '/src/components/pageTool/style/pbiSearchItem.less';
    :root {
        --scroll-display: none;
        --scroll-border-bottom: none;
        --scroll-border-bottom-fixed: none;
    }
    /deep/.searchItem .label{
        width: initial;
    }
    /deep/.ag-body-horizontal-scroll{
        border-bottom: var(--scroll-border-bottom) !important;
    }
    /deep/.ag-body-horizontal-scroll-viewport {
        display: var(--scroll-display) !important;
        border-bottom: var(--scroll-border-bottom) !important;
    }

    /deep/.ag-horizontal-left-spacer,
    /deep/.ag-horizontal-right-spacer{
        border-bottom: var(--scroll-border-bottom-fixed) !important;
    }

    /deep/.search-container .vue-treeselect__multi-value-label{
        white-space: nowrap;
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    /deep/.search-container .vue-treeselect__limit-tip-text{
        font-weight: initial;
        text-indent: -32px;
        overflow: hidden;
        margin: 0;
    }

    /* 复选框列样式 */
    /deep/ .ag-selection-checkbox {
        margin: 0 auto;
    }

    /deep/ .ag-header-select-all {
        margin: 0 auto;
    }

    /* 悬浮提示样式 */
    /deep/ .ag-tooltip {
        background-color: #ffffcc !important;
        border: 1px solid #ccc !important;
        border-radius: 4px !important;
        padding: 8px !important;
        font-size: 12px !important;
        line-height: 1.4 !important;
        white-space: pre-line !important;
        max-width: 300px !important;
        word-wrap: break-word !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    }
</style>
