<template>
  <a-modal
    centered
    title="编辑物料价格"
    :width="780"
    :visible="visible"
    :loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading">
      <a-form :form="form" labelAlign="right">
        <a-form-item style="display: none;" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['id']" />
        </a-form-item>

        <a-row :gutter="24">
          <!-- 料号 -->
          <a-col :span="12">
            <a-form-item label="料号" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input 
                placeholder="请输入料号" 
                v-decorator="['partNumber', {rules: [{required: true, message: '请输入料号！'}]}]" 
              />
            </a-form-item>
          </a-col>

          <!-- 物料规格描述 -->
          <a-col :span="12">
            <a-form-item label="物料规格" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input 
                placeholder="请输入物料规格" 
                v-decorator="['description', {rules: [{required: true, message: '请输入物料规格！'}]}]" 
              />
            </a-form-item>
          </a-col>

          <!-- 材料类型 -->
          <a-col :span="12">
            <a-form-item label="材料类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select 
                placeholder="请选择材料类型" 
                v-decorator="['materialType', {rules: [{required: true, message: '请选择材料类型！'}]}]"
              >
                <a-select-option 
                  v-for="(item, i) in getDict('bom_bill_material_type')" 
                  :value="item.code" 
                  :key="i"
                >
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 核算价格 -->
          <a-col :span="12">
            <a-form-item label="核算价格" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input-number 
                placeholder="请输入核算价格" 
                :min="0"
                :precision="2"
                style="width: 100%"
                v-decorator="['accountingPrice', {rules: [{required: true, message: '请输入核算价格！'}]}]" 
              />
            </a-form-item>
          </a-col>

          <!-- 单位 -->
          <a-col :span="12">
            <a-form-item label="单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input 
                placeholder="请输入单位" 
                v-decorator="['unit', {rules: [{required: true, message: '请输入单位！'}]}]" 
              />
            </a-form-item>
          </a-col>

          <!-- 结构件 -->
          <a-col :span="12">
            <a-form-item label="结构件" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select 
                placeholder="请选择是否结构件" 
                v-decorator="['structuralPart', {rules: [{required: true, message: '请选择是否结构件！'}]}]"
              >
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 备注 -->
          <a-col :span="24">
            <a-form-item label="备注" :labelCol="{span: 2}" :wrapperCol="{span: 21}">
              <a-textarea 
                :rows="2" 
                placeholder="请输入备注" 
                v-decorator="['remark']"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { materialPriceEdit, materialPriceCheckDuplicate } from "@/api/modular/system/materialPriceManage"
import { DICT_TYPE_TREE_DATA } from '@/store/mutation-types'
import Vue from 'vue'

export default {
  data() {
    return {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 }
      },
      visible: false,
      loading: false,
      form: this.$form.createForm(this)
    }
  },
  methods: {
    // 获取字典数据
    getDict(code) {
      const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
      return dictTypeTree ? dictTypeTree.filter(item => item.code === code)[0]?.children || [] : []
    },
    
    // 打开编辑弹窗
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.form.setFieldsValue({
          id: record.id,
          partNumber: record.partNumber,
          description: record.description,
          materialType: record.materialType + '',
          accountingPrice: record.accountingPrice,
          unit: record.unit,
          structuralPart: record.structuralPart,
          remark: record.remark
        })
      })
    },
    
    // 提交表单
    handleSubmit() {
      const { form: { validateFields } } = this

      validateFields((errors, values) => {
        if (!errors) {
          // 检查重复料号（排除当前记录）
          this.checkDuplicatePartNumber(values)
        }
      })
    },

    // 检查重复料号
    checkDuplicatePartNumber(values) {
      this.loading = true

      const checkParams = {
        partNumbers: [values.partNumber],
        excludeId: values.id // 排除当前记录
      }

      materialPriceCheckDuplicate(checkParams).then(res => {
        if (res.success && res.data && res.data.length > 0) {
          // 存在重复料号，显示确认对话框
          this.showDuplicateConfirmDialog(values)
        } else {
          // 没有重复，直接更新
          this.proceedWithEdit(values)
        }
      }).catch(error => {
        console.error('检查重复失败:', error)
        // 检查失败时也允许继续更新
        this.proceedWithEdit(values)
      }).finally(() => {
        this.loading = false
      })
    },

    // 显示重复确认对话框
    showDuplicateConfirmDialog(values) {
      this.$confirm({
        title: '发现重复的料号',
        content: `料号"${values.partNumber}"在数据库中已存在。\n\n点击"确认"将覆盖现有数据，点击"取消"将终止修改操作。`,
        okText: '确认覆盖',
        cancelText: '取消修改',
        onOk: () => {
          this.proceedWithEdit(values, true)
        },
        onCancel: () => {
          this.$message.info('已取消修改操作')
        }
      })
    },

    // 执行更新
    proceedWithEdit(values, forceOverride = false) {
      this.loading = true

      const editParams = {
        ...values,
        forceOverride: forceOverride
      }

      materialPriceEdit(editParams).then((res) => {
        if (res.success) {
          this.$message.success('更新成功')
          this.visible = false
          this.$emit('ok')
        } else {
          this.$message.error('更新失败：' + res.message)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    
    // 关闭弹窗
    handleCancel() {
      this.form.resetFields()
      this.visible = false
    }
  }
}
</script>