<template>
  <a-modal
    title="导入材料价格"
    :width="1000"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
  >
    <div>
      <!-- 文件上传区域 -->
      <div class="upload-section" v-if="!importData.length">
        <a-upload-dragger
          :fileList="fileList"
          :beforeUpload="beforeUpload"
          :remove="handleRemove"
          accept=".xlsx,.xls"
        >
          <p class="ant-upload-drag-icon">
            <a-icon type="inbox" />
          </p>
          <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p class="ant-upload-hint">
            支持单个文件上传，仅支持 .xlsx 和 .xls 格式
          </p>
        </a-upload-dragger>

        <div class="upload-actions" style="margin-top: 16px;">
          <a-button
            type="primary"
            :loading="uploadLoading"
            :disabled="!fileList.length"
            @click="handleUpload"
          >
            解析文件
          </a-button>
          <a-button style="margin-left: 8px;" @click="downloadTemplate">
            下载模板
          </a-button>
        </div>
      </div>

      <!-- 数据预览区域 -->
      <div v-if="importData.length">
        <div class="import-summary">
          <a-alert
            :message="`共解析 ${importData.length} 条数据，其中 ${validCount} 条有效，${errorCount} 条有错误`"
            :type="errorCount > 0 ? 'warning' : 'success'"
            show-icon
            style="margin-bottom: 16px;"
          />
        </div>

        <ag-grid-vue
          style="height: 400px;"
          class="ag-theme-balham"
          :columnDefs="columnDefs"
          :rowData="importData"
          :defaultColDef="defaultColDef"
          :suppressDragLeaveHidesColumns="true"
          :suppressMoveWhenColumnDragging="true"
        />

        <div class="import-actions" style="margin-top: 16px;">
          <a-button @click="resetImport">重新上传</a-button>
          <a-button
            type="primary"
            :disabled="validCount === 0"
            style="margin-left: 8px;"
            @click="handleImport"
          >
            导入有效数据 ({{ validCount }} 条)
          </a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { materialPriceImport, materialPriceBatchSave, materialPriceDownloadTemplate, materialPriceCheckDuplicate } from '@/api/modular/system/materialPriceManage'

export default {
  name: 'ImportMaterialPriceModal',
  data() {
    return {
      visible: false,
      confirmLoading: false,
      uploadLoading: false,
      fileList: [],
      importData: [],
      defaultColDef: {
        flex: 1,
        filter: false,
        floatingFilter: false,
        editable: false,
        resizable: true
      },
      columnDefs: [
        {
          headerName: "序号",
          width: 70,
          field: "no",
          cellRenderer: params => params.node.rowIndex + 1
        },
        {
          headerName: "材料类型",
          width: 120,
          field: "materialTypeText"
        },
        {
          headerName: "料号",
          width: 150,
          field: "partNumber"
        },
        {
          headerName: "物料规格",
          width: 200,
          field: "description"
        },
        {
          headerName: "结构件",
          width: 100,
          field: "structuralPartText"
        },
        {
          headerName: "核算价格(未税)",
          width: 140,
          field: "accountingPrice"
        },
        {
          headerName: "单位",
          width: 100,
          field: "unit"
        },
        {
          headerName: "备注",
          width: 150,
          field: "remark"
        },
        {
          headerName: "错误信息",
          width: 200,
          field: "errorMessage",
          cellStyle: params => {
            if (params.value) {
              return { color: 'red', fontWeight: 'bold' }
            }
            return null
          }
        }
      ]
    }
  },
  computed: {
    validCount() {
      return this.importData.filter(item => !item.errorMessage).length
    },
    errorCount() {
      return this.importData.filter(item => item.errorMessage).length
    }
  },
  methods: {
    show() {
      this.visible = true
      this.resetImport()
    },

    handleCancel() {
      this.visible = false
      this.resetImport()
    },

    handleOk() {
      this.handleCancel()
    },

    resetImport() {
      this.fileList = []
      this.importData = []
      this.confirmLoading = false
      this.uploadLoading = false
    },

    beforeUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel'
      if (!isExcel) {
        this.$message.error('只能上传 Excel 文件!')
        return false
      }

      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('文件大小不能超过 10MB!')
        return false
      }

      // 创建文件对象，确保包含原始文件引用
      const fileObj = {
        uid: file.uid || Date.now(),
        name: file.name,
        status: 'done',
        originFileObj: file // 保存原始文件对象
      }

      this.fileList = [fileObj]
      return false // 阻止自动上传
    },

    handleRemove() {
      this.fileList = []
    },

    async handleUpload() {
      if (!this.fileList.length) {
        this.$message.error('请选择要上传的文件')
        return
      }

      this.uploadLoading = true
      try {
        // 参考 testfee 的实现方式，创建 FormData
        const formData = new FormData()
        const file = this.fileList[0]

        // 获取原始文件对象
        const actualFile = file.originFileObj || file
        formData.append('file', actualFile)

        const response = await materialPriceImport(formData)
        if (response.success) {
          this.importData = response.data || []
          this.$message.success(`文件解析成功，共解析 ${this.importData.length} 条数据`)
        } else {
          this.$message.error('文件解析失败：' + response.message)
        }
      } catch (error) {
        console.error('导入失败:', error)
        this.$message.error('文件解析失败，请检查文件格式')
      } finally {
        this.uploadLoading = false
      }
    },

    async handleImport() {
      const validData = this.importData.filter(item => !item.errorMessage)
      if (validData.length === 0) {
        this.$message.error('没有有效数据可以导入')
        return
      }

      // 检查是否存在相同的料号
      this.checkDuplicatePartNumbers(validData)
    },

    // 检查重复的料号
    checkDuplicatePartNumbers(validData) {
      // 提取所有料号
      const partNumbers = validData.map(item => item.partNumber).filter(partNumber => partNumber)

      if (partNumbers.length === 0) {
        this.proceedWithImport(validData)
        return
      }

      this.confirmLoading = true
      materialPriceCheckDuplicate({ partNumbers }).then(res => {
        if (res.success && res.data && res.data.length > 0) {
          // 存在重复的料号，显示确认对话框
          const duplicatePartNumbers = res.data
          this.showDuplicateConfirmDialog(duplicatePartNumbers, validData)
        } else {
          // 没有重复，直接导入
          this.proceedWithImport(validData)
        }
      }).catch(error => {
        console.error('检查重复失败:', error)
        // 检查失败时也允许继续导入
        this.proceedWithImport(validData)
      }).finally(() => {
        this.confirmLoading = false
      })
    },

    // 显示重复确认对话框
    showDuplicateConfirmDialog(duplicatePartNumbers, validData) {
      const duplicateList = duplicatePartNumbers.join('、')

      this.$confirm({
        title: '发现重复的料号',
        content: `以下料号在数据库中已存在：${duplicateList}。\n\n点击"确认"将覆盖现有数据，点击"取消"将终止导入操作。`,
        okText: '确认覆盖',
        cancelText: '取消导入',
        onOk: () => {
          this.proceedWithImport(validData, true)
        },
        onCancel: () => {
          this.$message.info('已取消导入操作')
        }
      })
    },

    // 执行导入
    async proceedWithImport(validData, forceOverride = false) {
      this.confirmLoading = true

      try {
        const importParams = {
          data: validData,
          forceOverride: forceOverride
        }

        const response = await materialPriceBatchSave(importParams)
        if (response.success) {
          this.$message.success(`导入成功，共导入 ${validData.length} 条数据`)
          this.$emit('ok')
          this.handleCancel()
        } else {
          this.$message.error('导入失败：' + response.message)
        }
      } catch (error) {
        console.error('导入失败:', error)
        this.$message.error('导入失败，请重试')
      } finally {
        this.confirmLoading = false
      }
    },

    // 下载模板 - 参考 testfee/index.vue 的 downloadTemplate 方法
    downloadTemplate() {
      materialPriceDownloadTemplate({}).then(res => {
        const fileName = `材料价格导入模板.xlsx`
        const _res = res.data
        let blob = new Blob([_res])
        let downloadElement = document.createElement("a")
        //创建下载的链接
        let href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        //下载后文件名
        downloadElement.download = fileName
        document.body.appendChild(downloadElement)
        //点击下载
        downloadElement.click()
        //下载完成移除元素
        document.body.removeChild(downloadElement)
        //释放掉blob对象
        window.URL.revokeObjectURL(href)
      }).catch(error => {
        console.error('模板下载失败:', error)
        this.$message.error('模板下载失败，请重试')
      })
    }
  }
}
</script>

<style lang="less" scoped="">
.upload-section {
  text-align: center;
}

.import-summary {
  margin-bottom: 16px;
}

.import-actions {
  text-align: right;
}

/deep/ .ant-upload-drag {
  background-color: #fafafa;
}
</style>
